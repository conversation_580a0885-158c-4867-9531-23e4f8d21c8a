package com.gec.wiki.controller;

import com.gec.wiki.pojo.req.TechnicianQueryReq;
import com.gec.wiki.pojo.req.TechnicianSaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.TechnicianQueryResp;
import com.gec.wiki.service.TechnicianService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/shop/technician")
@CrossOrigin
public class TechnicianController {
    
    private static final Logger LOG = LoggerFactory.getLogger(TechnicianController.class);
    
    @Resource
    private TechnicianService technicianService;
    
    /**
     * 分页查询技师列表
     */
    @GetMapping("/list")
    public CommonResp<PageResp<TechnicianQueryResp>> list(@Valid TechnicianQueryReq req) {
        LOG.info("技师列表查询开始: {}", req);
        
        CommonResp<PageResp<TechnicianQueryResp>> resp = new CommonResp<>();
        PageResp<TechnicianQueryResp> list = technicianService.list(req);
        resp.setContent(list);
        
        LOG.info("技师列表查询结束: 总数={}", list.getTotal());
        return resp;
    }
    
    /**
     * 保存技师信息（新增或更新）
     */
    @PostMapping("/save")
    public CommonResp<Void> save(@Valid @RequestBody TechnicianSaveReq req) {
        LOG.info("保存技师信息开始: {}", req);
        
        technicianService.save(req);
        
        CommonResp<Void> resp = new CommonResp<>();
        LOG.info("保存技师信息结束");
        return resp;
    }
    
    /**
     * 根据ID查询技师详情
     */
    @GetMapping("/{id}")
    public CommonResp<TechnicianQueryResp> getById(@PathVariable Long id, @RequestParam Long shopUserId) {
        LOG.info("查询技师详情开始: id={}, shopUserId={}", id, shopUserId);
        
        CommonResp<TechnicianQueryResp> resp = new CommonResp<>();
        TechnicianQueryResp technician = technicianService.getById(id, shopUserId);
        resp.setContent(technician);
        
        LOG.info("查询技师详情结束");
        return resp;
    }
    
    /**
     * 删除技师
     */
    @DeleteMapping("/{id}")
    public CommonResp<Void> deleteById(@PathVariable Long id, @RequestParam Long shopUserId) {
        LOG.info("删除技师开始: id={}, shopUserId={}", id, shopUserId);
        
        technicianService.deleteById(id, shopUserId);
        
        CommonResp<Void> resp = new CommonResp<>();
        LOG.info("删除技师结束");
        return resp;
    }
    
    /**
     * 批量删除技师
     */
    @DeleteMapping("/batch")
    public CommonResp<Void> deleteByIds(@RequestBody List<Long> ids, @RequestParam Long shopUserId) {
        LOG.info("批量删除技师开始: ids={}, shopUserId={}", ids, shopUserId);
        
        technicianService.deleteByIds(ids, shopUserId);
        
        CommonResp<Void> resp = new CommonResp<>();
        LOG.info("批量删除技师结束");
        return resp;
    }
    
    /**
     * 更新技师状态
     */
    @PutMapping("/{id}/status")
    public CommonResp<Void> updateStatus(@PathVariable Long id, 
                                        @RequestParam Integer status, 
                                        @RequestParam Long shopUserId) {
        LOG.info("更新技师状态开始: id={}, status={}, shopUserId={}", id, status, shopUserId);
        
        technicianService.updateStatus(id, status, shopUserId);
        
        CommonResp<Void> resp = new CommonResp<>();
        LOG.info("更新技师状态结束");
        return resp;
    }
    
    /**
     * 获取技师统计信息
     */
    @GetMapping("/stats")
    public CommonResp<Map<String, Object>> getStats(@RequestParam Long shopUserId) {
        LOG.info("获取技师统计信息开始: shopUserId={}", shopUserId);
        
        CommonResp<Map<String, Object>> resp = new CommonResp<>();
        Map<String, Object> stats = technicianService.getStats(shopUserId);
        resp.setContent(stats);
        
        LOG.info("获取技师统计信息结束");
        return resp;
    }
    
    /**
     * 获取在线技师列表
     */
    @GetMapping("/online")
    public CommonResp<List<TechnicianQueryResp>> getOnlineTechnicians(@RequestParam Long shopUserId) {
        LOG.info("获取在线技师列表开始: shopUserId={}", shopUserId);
        
        CommonResp<List<TechnicianQueryResp>> resp = new CommonResp<>();
        List<TechnicianQueryResp> list = technicianService.getOnlineTechnicians(shopUserId);
        resp.setContent(list);
        
        LOG.info("获取在线技师列表结束: 数量={}", list.size());
        return resp;
    }
    
    /**
     * 根据技能搜索技师
     */
    @PostMapping("/search-by-skills")
    public CommonResp<List<TechnicianQueryResp>> searchBySkills(@RequestBody List<String> skills, 
                                                               @RequestParam Long shopUserId) {
        LOG.info("根据技能搜索技师开始: skills={}, shopUserId={}", skills, shopUserId);
        
        CommonResp<List<TechnicianQueryResp>> resp = new CommonResp<>();
        List<TechnicianQueryResp> list = technicianService.searchBySkills(skills, shopUserId);
        resp.setContent(list);
        
        LOG.info("根据技能搜索技师结束: 数量={}", list.size());
        return resp;
    }
    
    /**
     * 获取技师业绩信息
     */
    @GetMapping("/{id}/performance")
    public CommonResp<Map<String, Object>> getPerformance(@PathVariable Long id, 
                                                          @RequestParam Long shopUserId) {
        LOG.info("获取技师业绩信息开始: technicianId={}, shopUserId={}", id, shopUserId);
        
        CommonResp<Map<String, Object>> resp = new CommonResp<>();
        Map<String, Object> performance = technicianService.getPerformance(id, shopUserId);
        resp.setContent(performance);
        
        LOG.info("获取技师业绩信息结束");
        return resp;
    }
    
    /**
     * 检查电话号码是否已存在
     */
    @GetMapping("/check-phone")
    public CommonResp<Boolean> checkPhoneExists(@RequestParam String phone,
                                               @RequestParam(required = false) Long excludeId,
                                               @RequestParam Long shopUserId) {
        LOG.info("检查电话号码是否存在: phone={}, excludeId={}, shopUserId={}", phone, excludeId, shopUserId);
        
        CommonResp<Boolean> resp = new CommonResp<>();
        boolean exists = technicianService.checkPhoneExists(phone, excludeId, shopUserId);
        resp.setContent(exists);
        
        LOG.info("检查电话号码结果: {}", exists);
        return resp;
    }
}
