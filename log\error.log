2025-09-12 11:10:37.148 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4855805940204576  [0;39m 根据token未找到用户信息，token可能已过期: Bearer 43b84acf69e24fdf8119971420c9451d
2025-09-12 15:06:44.598 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856270179173408  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:21:20.762 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856298889151520  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:23:37.492 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856303369847840  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:39:27.314 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

