package com.gec.wiki.mapper;

import com.gec.wiki.pojo.Technician;
import com.gec.wiki.pojo.req.TechnicianQueryReq;
import com.gec.wiki.pojo.resp.TechnicianQueryResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TechnicianMapper {
    
    /**
     * 根据ID查询技师
     */
    Technician selectById(Long id);
    
    /**
     * 分页查询技师列表
     */
    List<TechnicianQueryResp> selectList(TechnicianQueryReq req);
    
    /**
     * 查询技师总数
     */
    int selectCount(TechnicianQueryReq req);
    
    /**
     * 新增技师
     */
    int insert(Technician technician);
    
    /**
     * 更新技师信息
     */
    int updateById(Technician technician);
    
    /**
     * 删除技师
     */
    int deleteById(Long id);
    
    /**
     * 根据维修店用户ID删除所有技师
     */
    int deleteByShopUserId(Long shopUserId);
    
    /**
     * 检查电话号码是否存在
     */
    int countByPhone(@Param("phone") String phone, @Param("excludeId") Long excludeId, @Param("shopUserId") Long shopUserId);
    
    /**
     * 更新技师状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 获取技师统计信息
     */
    Map<String, Object> selectStats(@Param("shopUserId") Long shopUserId);
    
    /**
     * 获取技师业绩信息
     */
    Map<String, Object> selectPerformance(@Param("technicianId") Long technicianId, @Param("shopUserId") Long shopUserId);
    
    /**
     * 批量更新技师评分
     */
    int batchUpdateRating(@Param("updates") List<Map<String, Object>> updates);
    
    /**
     * 根据技能搜索技师
     */
    List<TechnicianQueryResp> selectBySkills(@Param("skills") List<String> skills, @Param("shopUserId") Long shopUserId);
    
    /**
     * 获取在线技师列表
     */
    List<TechnicianQueryResp> selectOnlineTechnicians(@Param("shopUserId") Long shopUserId);
}
