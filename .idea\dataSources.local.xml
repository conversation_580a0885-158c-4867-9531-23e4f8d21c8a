<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="IU-243.26053.27">
    <data-source name="car_service@localhost" uuid="22a845b2-e507-4e35-ae48-123c2810419c">
      <database-info product="MySQL" version="5.7.36" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)" dbms="MYSQL" exact-version="5.7.36" exact-driver-version="8.0">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>