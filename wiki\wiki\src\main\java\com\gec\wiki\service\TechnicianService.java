package com.gec.wiki.service;

import com.gec.wiki.pojo.req.TechnicianQueryReq;
import com.gec.wiki.pojo.req.TechnicianSaveReq;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.TechnicianQueryResp;

import java.util.List;
import java.util.Map;

public interface TechnicianService {
    
    /**
     * 分页查询技师列表
     */
    PageResp<TechnicianQueryResp> list(TechnicianQueryReq req);
    
    /**
     * 保存技师信息（新增或更新）
     */
    void save(TechnicianSaveReq req);
    
    /**
     * 根据ID查询技师详情
     */
    TechnicianQueryResp getById(Long id, Long shopUserId);
    
    /**
     * 删除技师
     */
    void deleteById(Long id, Long shopUserId);
    
    /**
     * 批量删除技师
     */
    void deleteByIds(List<Long> ids, Long shopUserId);
    
    /**
     * 更新技师状态
     */
    void updateStatus(Long id, Integer status, Long shopUserId);
    
    /**
     * 获取技师统计信息
     */
    Map<String, Object> getStats(Long shopUserId);
    
    /**
     * 获取在线技师列表
     */
    List<TechnicianQueryResp> getOnlineTechnicians(Long shopUserId);
    
    /**
     * 根据技能搜索技师
     */
    List<TechnicianQueryResp> searchBySkills(List<String> skills, Long shopUserId);
    
    /**
     * 获取技师业绩信息
     */
    Map<String, Object> getPerformance(Long technicianId, Long shopUserId);
    
    /**
     * 检查电话号码是否已存在
     */
    boolean checkPhoneExists(String phone, Long excludeId, Long shopUserId);
}
