2025-09-11 14:25:20.126 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 6520 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-11 14:25:20.126 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-11 14:25:20.952 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-11 14:25:20.959 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-11 14:25:21.009 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-09-11 14:25:21.967 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-11 14:25:21.994 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-11 14:25:21.994 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-11 14:25:21.994 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-11 14:25:22.107 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-11 14:25:22.107 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1915 ms
2025-09-11 14:25:23.655 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-11 14:25:23.964 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-11 14:25:23.964 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8880 is already in use
2025-09-11 14:25:23.988 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-11 14:25:23.988 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8880"]
2025-09-11 14:25:23.988 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-11 14:25:24.123 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8880"]
2025-09-11 14:25:24.123 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8880"]
2025-09-11 14:25:24.131 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-11 14:25:24.143 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-09-11 14:26:16.610 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 12276 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-11 14:26:16.610 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-11 14:26:17.213 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-11 14:26:17.229 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-11 14:26:17.261 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-09-11 14:26:17.961 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-11 14:26:17.982 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-11 14:26:17.982 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-11 14:26:17.982 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-11 14:26:18.066 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-11 14:26:18.066 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1405 ms
2025-09-11 14:26:19.316 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-11 14:26:19.586 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-11 14:26:19.618 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-11 14:26:19.629 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.398 seconds (JVM running for 3.846)
2025-09-11 14:26:19.629 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-11 14:26:19.629 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-11 17:12:42.495 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 14916 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-11 17:12:42.500 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-11 17:12:43.172 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-11 17:12:43.172 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-11 17:12:43.203 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-09-11 17:12:44.134 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-11 17:12:44.147 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-11 17:12:44.148 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-11 17:12:44.149 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-11 17:12:44.246 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-11 17:12:44.246 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1698 ms
2025-09-11 17:12:45.653 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-11 17:12:45.949 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-11 17:12:45.957 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8880 is already in use
2025-09-11 17:12:45.976 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-11 17:12:45.976 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8880"]
2025-09-11 17:12:45.976 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-11 17:12:46.099 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8880"]
2025-09-11 17:12:46.099 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8880"]
2025-09-11 17:12:46.108 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-11 17:12:46.122 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-09-11 17:15:45.677 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 6052 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki\wiki)
2025-09-11 17:15:45.679 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-11 17:15:46.255 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-11 17:15:46.255 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-11 17:15:46.286 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-09-11 17:15:46.986 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-11 17:15:47.002 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-11 17:15:47.002 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-11 17:15:47.002 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-11 17:15:47.087 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-11 17:15:47.087 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1359 ms
2025-09-11 17:15:48.317 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-11 17:15:48.588 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-11 17:15:48.631 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-11 17:15:48.638 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 3.329 seconds (JVM running for 3.771)
2025-09-11 17:15:48.640 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-11 17:15:48.640 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-11 17:16:55.285 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-11 17:16:55.285 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-11 17:16:55.285 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-11 17:16:55.302 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853694965416992  [0;39m ------------- 开始 -------------
2025-09-11 17:16:55.302 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853694965416992  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-11 17:16:55.317 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853694965416992  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-11 17:16:55.317 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853694965416992  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:16:55.349 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853694965416992  [0;39m 请求参数: []
2025-09-11 17:16:55.349 INFO  com.gec.wiki.controller.ShopController            :85   [32m4853694965416992  [0;39m 🏪 获取所有维修店列表
2025-09-11 17:16:55.503 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4853694965416992  [0;39m {dataSource-1} inited
2025-09-11 17:16:55.704 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4853694965416992  [0;39m 获取到 3 家营业中的维修店
2025-09-11 17:16:55.704 INFO  com.gec.wiki.controller.ShopController            :106  [32m4853694965416992  [0;39m ✅ 获取到 3 家维修店
2025-09-11 17:16:55.729 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853694965416992  [0;39m 返回结果: {"content":[{"phone":"***********","name":"陆丁","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"恶梦","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"wei","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-11 17:16:55.729 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853694965416992  [0;39m ------------- 结束 耗时：427 ms -------------
2025-09-11 17:18:57.098 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853698956428321  [0;39m ------------- 开始 -------------
2025-09-11 17:18:57.098 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853698956428320  [0;39m ------------- 开始 -------------
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853698956428321  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853698956428320  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853698956428321  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853698956428320  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853698956428321  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:18:57.100 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853698956428320  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:18:57.105 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853698956428320  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-11 17:18:57.105 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853698956428321  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-11 17:18:57.110 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4853698956428320  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-11 17:18:57.110 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4853698956428321  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-11 17:18:57.118 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4853698956428320  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-11 17:18:57.118 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4853698956428321  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-11 17:18:57.120 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4853698956428320  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-11 17:18:57.120 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4853698956428321  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-11 17:18:57.194 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4853698956428320  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 121502
2025-09-11 17:18:57.205 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4853698956428321  [0;39m 📋 数据库查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:18:57.205 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4853698956428320  [0;39m 📋 数据库查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:18:57.238 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4853698956428320  [0;39m ✅ 服务查询完成，返回 2 条记录
2025-09-11 17:18:57.238 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4853698956428321  [0;39m ✅ 服务查询完成，返回 2 条记录
2025-09-11 17:18:57.242 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4853698956428321  [0;39m 📊 查询结果：共 2 条记录
2025-09-11 17:18:57.242 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4853698956428320  [0;39m 📊 查询结果：共 2 条记录
2025-09-11 17:18:57.255 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853698956428321  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:18:57.255 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853698956428320  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:18:57.255 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853698956428321  [0;39m ------------- 结束 耗时：157 ms -------------
2025-09-11 17:18:57.255 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853698956428320  [0;39m ------------- 结束 耗时：157 ms -------------
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699016033312  [0;39m ------------- 开始 -------------
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699016033312  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699016033312  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699016033312  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699016033312  [0;39m 请求参数: []
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699016033313  [0;39m ------------- 开始 -------------
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699016033313  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699016033313  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699016033313  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:18:58.917 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699016033313  [0;39m 请求参数: [1,10,""]
2025-09-11 17:18:58.933 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4853699016033312  [0;39m 获取维修店1预约统计成功
2025-09-11 17:18:58.933 INFO  com.gec.wiki.controller.OrderController           :213  [32m4853699016033312  [0;39m 返回真实数据库统计数据: {monthlyRevenue=60.00, completedOrders=1, pendingOrders=0, processingOrders=2}
2025-09-11 17:18:58.933 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699016033312  [0;39m 返回结果: {"content":{"monthlyRevenue":60.00,"completedOrders":1,"pendingOrders":0,"processingOrders":2},"message":"获取统计数据成功","success":true}
2025-09-11 17:18:58.933 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699016033312  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-11 17:18:58.949 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4853699016033313  [0;39m 获取维修店1预约列表成功，共4条
2025-09-11 17:18:58.949 INFO  com.gec.wiki.controller.OrderController           :65   [32m4853699016033313  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-11 17:18:58.949 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699016033313  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":2},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-11 17:18:58.949 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699016033313  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699064267808  [0;39m ------------- 开始 -------------
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699064267809  [0;39m ------------- 开始 -------------
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699064267808  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699064267808  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699064267809  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699064267808  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699064267809  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699064267808  [0;39m 请求参数: []
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699064267809  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:00.389 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699064267809  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-11 17:19:00.389 INFO  com.gec.wiki.controller.ShopServiceController     :83   [32m4853699064267808  [0;39m 📋 获取服务分类列表
2025-09-11 17:19:00.389 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4853699064267809  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-11 17:19:00.389 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4853699064267809  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}, shopId: 1
2025-09-11 17:19:00.389 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4853699064267809  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-11 17:19:00.389 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4853699064267808  [0;39m 🌲 构建分类树形结构
2025-09-11 17:19:00.423 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4853699064267808  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-11 17:19:00.433 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4853699064267809  [0;39m 📋 维修店 1 查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:00.436 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699064267808  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":4847921662395424,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":4848314840974368,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-11 17:19:00.436 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699064267808  [0;39m ------------- 结束 耗时：47 ms -------------
2025-09-11 17:19:00.442 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4853699064267809  [0;39m ✅ 维修店 1 服务查询完成，返回 2 条记录
2025-09-11 17:19:00.443 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4853699064267809  [0;39m ✅ 维修店 1 查询到 2 条服务记录
2025-09-11 17:19:00.444 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699064267809  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:00.445 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699064267809  [0;39m ------------- 结束 耗时：56 ms -------------
2025-09-11 17:19:07.324 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699291513888  [0;39m ------------- 开始 -------------
2025-09-11 17:19:07.324 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699291513889  [0;39m ------------- 开始 -------------
2025-09-11 17:19:07.324 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699291513889  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-11 17:19:07.324 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699291513888  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-11 17:19:07.325 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699291513889  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-11 17:19:07.325 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699291513888  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-11 17:19:07.325 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699291513889  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:07.326 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699291513888  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:07.327 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699291513888  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-11 17:19:07.327 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699291513889  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-11 17:19:07.327 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4853699291513888  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-11 17:19:07.327 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4853699291513889  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-11 17:19:07.327 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4853699291513888  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=1000}
2025-09-11 17:19:07.327 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4853699291513889  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=6}
2025-09-11 17:19:07.328 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4853699291513888  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-11 17:19:07.328 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4853699291513889  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-11 17:19:07.340 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4853699291513889  [0;39m 📋 数据库查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:07.340 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4853699291513888  [0;39m 📋 数据库查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:07.351 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4853699291513889  [0;39m ✅ 服务查询完成，返回 2 条记录
2025-09-11 17:19:07.351 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4853699291513888  [0;39m ✅ 服务查询完成，返回 2 条记录
2025-09-11 17:19:07.352 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4853699291513889  [0;39m 📊 查询结果：共 2 条记录
2025-09-11 17:19:07.352 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4853699291513888  [0;39m 📊 查询结果：共 2 条记录
2025-09-11 17:19:07.353 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699291513888  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:07.353 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699291513889  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:07.354 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699291513888  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-11 17:19:07.354 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699291513889  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-11 17:19:27.981 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853699968402464  [0;39m ------------- 开始 -------------
2025-09-11 17:19:27.981 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853699968402464  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-11 17:19:27.987 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853699968402464  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-11 17:19:27.988 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853699968402464  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:27.988 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853699968402464  [0;39m 请求参数: [{"username":"韦茹萍"}]
2025-09-11 17:19:27.994 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4853699968402464  [0;39m 🔐 用户登录尝试：username=韦茹萍, ip=0:0:0:0:0:0:0:1
2025-09-11 17:19:27.994 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4853699968402464  [0;39m 🔒 检查用户锁定状态：username=韦茹萍
2025-09-11 17:19:28.001 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :48   [32m4853699968402464  [0;39m ✅ 用户未被锁定，无锁定记录：username=韦茹萍
2025-09-11 17:19:28.078 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :78   [32m4853699968402464  [0;39m 📝 记录登录失败：username=韦茹萍, userId=5
2025-09-11 17:19:28.106 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :95   [32m4853699968402464  [0;39m ➕ 创建新的登录失败记录：username=韦茹萍, failCount=1
2025-09-11 17:19:28.112 WARN  com.gec.wiki.service.impl.UserServiceImpl         :122  [32m4853699968402464  [0;39m ❌ 密码错误：username=韦茹萍, failCount=1/5
2025-09-11 17:19:28.112 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853699968402464  [0;39m 返回结果: {"message":"密码错误，连续错误 1/5 次","success":false}
2025-09-11 17:19:28.112 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853699968402464  [0;39m ------------- 结束 耗时：131 ms -------------
2025-09-11 17:19:35.761 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700223337504  [0;39m ------------- 开始 -------------
2025-09-11 17:19:35.762 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700223337504  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-11 17:19:35.763 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700223337504  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-11 17:19:35.763 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700223337504  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:35.763 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700223337504  [0;39m 请求参数: [{"username":"韦茹萍"}]
2025-09-11 17:19:35.765 INFO  com.gec.wiki.service.impl.UserServiceImpl         :65   [32m4853700223337504  [0;39m 🔐 用户登录尝试：username=韦茹萍, ip=0:0:0:0:0:0:0:1
2025-09-11 17:19:35.765 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4853700223337504  [0;39m 🔒 检查用户锁定状态：username=韦茹萍
2025-09-11 17:19:35.765 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :65   [32m4853700223337504  [0;39m ✅ 用户未被锁定：username=韦茹萍
2025-09-11 17:19:35.836 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4853700223337504  [0;39m 🧹 清除登录失败记录：username=韦茹萍
2025-09-11 17:19:35.855 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4853700223337504  [0;39m ✅ 成功清除登录失败记录：username=韦茹萍, 清除记录数=1
2025-09-11 17:19:35.856 INFO  com.gec.wiki.service.impl.UserServiceImpl         :162  [32m4853700223337504  [0;39m ✅ 用户登录成功：username=韦茹萍, userType=1, ip=0:0:0:0:0:0:0:1
2025-09-11 17:19:35.856 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700223337504  [0;39m 返回结果: {"content":{"userInfo":{"realName":"韦茹萍","phone":"19978452934","id":5,"userType":1,"email":"<EMAIL>","username":"韦茹萍"},"token":"f66c0e8f4cb8445a85607a30d943433b"},"message":"登录成功","success":true}
2025-09-11 17:19:35.856 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700223337504  [0;39m ------------- 结束 耗时：95 ms -------------
2025-09-11 17:19:35.951 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700229563424  [0;39m ------------- 开始 -------------
2025-09-11 17:19:35.952 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700229563424  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-11 17:19:35.952 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700229563424  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-11 17:19:35.953 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700229563424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:35.953 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700229563424  [0;39m 请求参数: [{"page":1,"size":4}]
2025-09-11 17:19:35.954 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4853700229563424  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=4}
2025-09-11 17:19:35.955 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4853700229563424  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=4}
2025-09-11 17:19:35.956 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :79   [32m4853700229563424  [0;39m 🔢 执行分页查询：页码=1, 页大小=4
2025-09-11 17:19:35.970 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :83   [32m4853700229563424  [0;39m 📋 数据库查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:35.981 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :99   [32m4853700229563424  [0;39m ✅ 服务查询完成，返回 2 条记录
2025-09-11 17:19:35.982 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4853700229563424  [0;39m 📊 查询结果：共 2 条记录
2025-09-11 17:19:35.983 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700229563424  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:35.984 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700229563424  [0;39m ------------- 结束 耗时：33 ms -------------
2025-09-11 17:19:40.087 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700365091872  [0;39m ------------- 开始 -------------
2025-09-11 17:19:40.088 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700365091872  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-11 17:19:40.088 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700365091872  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-11 17:19:40.089 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700365091872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:40.091 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700365091872  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-11 17:19:40.111 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4853700365091872  [0;39m 总行数：3
2025-09-11 17:19:40.111 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4853700365091872  [0;39m 总页数：1
2025-09-11 17:19:40.124 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700365091872  [0;39m 返回结果: {"content":{"list":[{"brand":"东风日产","color":"黑色","createTime":"2025-09-10T22:57:11","engineNumber":"东风日产300","id":4850817489077285,"isDefault":1,"licensePlate":"22333","mileage":500,"model":"e300","status":1,"updateTime":"2025-09-11T12:55:09","userId":5,"vin":"6666","year":2022},{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":4850817489077286,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T12:46:22","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":0,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T12:58:12","userId":5}],"total":3},"success":true}
2025-09-11 17:19:40.128 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700365091872  [0;39m ------------- 结束 耗时：41 ms -------------
2025-09-11 17:19:43.251 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700468769824  [0;39m ------------- 开始 -------------
2025-09-11 17:19:43.251 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700468769824  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-11 17:19:43.252 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700468769824  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-11 17:19:43.252 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700468769824  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:43.252 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700468769824  [0;39m 请求参数: []
2025-09-11 17:19:43.253 INFO  com.gec.wiki.controller.ShopController            :85   [32m4853700468769824  [0;39m 🏪 获取所有维修店列表
2025-09-11 17:19:43.258 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4853700468769824  [0;39m 获取到 3 家营业中的维修店
2025-09-11 17:19:43.259 INFO  com.gec.wiki.controller.ShopController            :106  [32m4853700468769824  [0;39m ✅ 获取到 3 家维修店
2025-09-11 17:19:43.259 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700468769824  [0;39m 返回结果: {"content":[{"phone":"***********","name":"陆丁","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"恶梦","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"wei","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-11 17:19:43.259 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700468769824  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-11 17:19:43.282 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700469785632  [0;39m ------------- 开始 -------------
2025-09-11 17:19:43.282 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700469785632  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-11 17:19:43.283 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700469785632  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-11 17:19:43.283 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700469785632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:43.284 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700469785632  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-11 17:19:43.294 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4853700469785632  [0;39m 总行数：3
2025-09-11 17:19:43.294 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4853700469785632  [0;39m 总页数：1
2025-09-11 17:19:43.295 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700469785632  [0;39m 返回结果: {"content":{"list":[{"brand":"东风日产","color":"黑色","createTime":"2025-09-10T22:57:11","engineNumber":"东风日产300","id":4850817489077285,"isDefault":1,"licensePlate":"22333","mileage":500,"model":"e300","status":1,"updateTime":"2025-09-11T12:55:09","userId":5,"vin":"6666","year":2022},{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":4850817489077286,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T12:46:22","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":0,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T12:58:12","userId":5}],"total":3},"success":true}
2025-09-11 17:19:43.296 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700469785632  [0;39m ------------- 结束 耗时：14 ms -------------
2025-09-11 17:19:48.461 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700639491104  [0;39m ------------- 开始 -------------
2025-09-11 17:19:48.462 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700639491104  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-11 17:19:48.463 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700639491104  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-11 17:19:48.463 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700639491104  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:48.464 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700639491104  [0;39m 请求参数: [{"page":1,"size":100}]
2025-09-11 17:19:48.464 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4853700639491104  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=100}
2025-09-11 17:19:48.465 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4853700639491104  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=100}, shopId: 1
2025-09-11 17:19:48.466 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4853700639491104  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=100
2025-09-11 17:19:48.476 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4853700639491104  [0;39m 📋 维修店 1 查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:48.482 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4853700639491104  [0;39m ✅ 维修店 1 服务查询完成，返回 2 条记录
2025-09-11 17:19:48.482 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4853700639491104  [0;39m ✅ 维修店 1 查询到 2 条服务记录
2025-09-11 17:19:48.482 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700639491104  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:48.482 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700639491104  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-11 17:19:55.233 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4853700861396000  [0;39m ------------- 开始 -------------
2025-09-11 17:19:55.238 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4853700861396000  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-11 17:19:55.238 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4853700861396000  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-11 17:19:55.238 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4853700861396000  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-11 17:19:55.239 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4853700861396000  [0;39m 请求参数: [{"page":1,"size":100}]
2025-09-11 17:19:55.240 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4853700861396000  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=100}
2025-09-11 17:19:55.240 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4853700861396000  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=100}, shopId: 1
2025-09-11 17:19:55.240 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4853700861396000  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=100
2025-09-11 17:19:55.247 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4853700861396000  [0;39m 📋 维修店 1 查询结果：共 2 条记录，当前页 2 条
2025-09-11 17:19:55.252 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4853700861396000  [0;39m ✅ 维修店 1 服务查询完成，返回 2 条记录
2025-09-11 17:19:55.252 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4853700861396000  [0;39m ✅ 维修店 1 查询到 2 条服务记录
2025-09-11 17:19:55.258 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4853700861396000  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":2},"message":"查询成功","success":true}
2025-09-11 17:19:55.259 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4853700861396000  [0;39m ------------- 结束 耗时：26 ms -------------
