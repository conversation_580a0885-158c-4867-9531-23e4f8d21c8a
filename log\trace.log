2025-09-12 10:29:17.489 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 26748 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 10:29:17.497 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 10:29:18.687 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 10:29:18.693 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 10:29:18.747 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-09-12 10:29:19.583 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 10:29:19.598 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 10:29:19.599 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 10:29:19.599 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 10:29:19.756 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 10:29:19.756 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 2172 ms
2025-09-12 10:29:22.691 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 10:29:23.184 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 10:29:23.226 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 10:29:23.237 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 6.357 seconds (JVM running for 7.835)
2025-09-12 10:29:23.241 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 10:29:23.241 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 10:29:40.233 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 10:29:40.233 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 10:29:40.233 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-12 10:29:40.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855725436011553  [0;39m ------------- 开始 -------------
2025-09-12 10:29:40.347 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855725436011552  [0;39m ------------- 开始 -------------
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855725436011553  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855725436011552  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855725436011552  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855725436011553  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855725436011552  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:29:40.353 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855725436011553  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:29:40.461 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855725436011552  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:29:40.461 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855725436011553  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:29:40.473 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855725436011552  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:29:40.473 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855725436011553  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:29:40.477 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855725436011553  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:29:40.477 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855725436011552  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:29:40.494 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855725436011553  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:29:40.494 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855725436011552  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:29:40.677 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4855725436011552  [0;39m {dataSource-1} inited
2025-09-12 10:29:41.122 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855725436011552  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:29:41.122 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855725436011553  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:29:41.147 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855725436011552  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:29:41.147 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855725436011553  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:29:41.147 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855725436011553  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:29:41.147 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855725436011552  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:29:41.164 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855725436011553  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:29:41.164 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855725436011552  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:29:41.164 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855725436011553  [0;39m ------------- 结束 耗时：817 ms -------------
2025-09-12 10:29:41.164 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855725436011552  [0;39m ------------- 结束 耗时：817 ms -------------
2025-09-12 10:29:44.255 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855725564068896  [0;39m ------------- 开始 -------------
2025-09-12 10:29:44.255 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855725564068896  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:29:44.255 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855725564068896  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:29:44.255 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855725564068896  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:29:44.260 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855725564068896  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-12 10:29:44.288 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855725564068896  [0;39m 总行数：2
2025-09-12 10:29:44.288 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855725564068896  [0;39m 总页数：1
2025-09-12 10:29:44.294 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855725564068896  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:29:44.294 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855725564068896  [0;39m ------------- 结束 耗时：39 ms -------------
2025-09-12 10:29:45.862 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855725616727072  [0;39m ------------- 开始 -------------
2025-09-12 10:29:45.865 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855725616727072  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:29:45.865 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855725616727072  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:29:45.865 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855725616727072  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:29:45.865 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855725616727072  [0;39m 请求参数: []
2025-09-12 10:29:45.869 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855725616727072  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:29:45.883 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855725616727072  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:29:45.887 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855725616727072  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:29:45.906 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855725616727072  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:29:45.906 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855725616727072  [0;39m ------------- 结束 耗时：44 ms -------------
2025-09-12 10:29:45.945 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855725619446816  [0;39m ------------- 开始 -------------
2025-09-12 10:29:45.948 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855725619446816  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:29:45.948 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855725619446816  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:29:45.948 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855725619446816  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:29:45.950 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855725619446816  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:29:45.964 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855725619446816  [0;39m 总行数：2
2025-09-12 10:29:45.971 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855725619446816  [0;39m 总页数：1
2025-09-12 10:29:45.971 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855725619446816  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:29:45.971 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855725619446816  [0;39m ------------- 结束 耗时：26 ms -------------
2025-09-12 10:30:25.269 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855726908015648  [0;39m ------------- 开始 -------------
2025-09-12 10:30:25.269 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855726908015648  [0;39m 请求地址: http://localhost:8880/service/getAllServiceList GET
2025-09-12 10:30:25.269 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855726908015648  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getAllServiceList
2025-09-12 10:30:25.273 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855726908015648  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:30:25.274 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855726908015648  [0;39m 请求参数: [{"shopId":6,"status":1}]
2025-09-12 10:30:25.274 INFO  com.gec.wiki.controller.ServiceController         :69   [32m4855726908015648  [0;39m 查询所有服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=1, isRecommend=null, shopId=6, page=null, size=null}
2025-09-12 10:30:25.292 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855726908015648  [0;39m 返回结果: {"content":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"}],"message":"查询成功","success":true}
2025-09-12 10:30:25.292 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855726908015648  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-12 10:37:25.032 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855740662809632  [0;39m ------------- 开始 -------------
2025-09-12 10:37:25.032 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855740662809632  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:37:25.032 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855740662809632  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:37:25.032 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855740662809632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:25.032 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855740662809632  [0;39m 请求参数: []
2025-09-12 10:37:25.032 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855740662809632  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:37:25.067 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855740662809632  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 419761
2025-09-12 10:37:25.073 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855740662809632  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 463926
2025-09-12 10:37:25.088 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855740662809632  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:37:25.088 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855740662809632  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:37:25.088 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855740662809632  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:37:25.090 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855740662809632  [0;39m ------------- 结束 耗时：58 ms -------------
2025-09-12 10:37:25.104 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855740665168928  [0;39m ------------- 开始 -------------
2025-09-12 10:37:25.104 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855740665168928  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:37:25.104 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855740665168928  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:37:25.106 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855740665168928  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:25.106 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855740665168928  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:37:25.117 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855740665168928  [0;39m 总行数：2
2025-09-12 10:37:25.117 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855740665168928  [0;39m 总页数：1
2025-09-12 10:37:25.117 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855740665168928  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:37:25.117 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855740665168928  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-12 10:37:37.572 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855741073720352  [0;39m ------------- 开始 -------------
2025-09-12 10:37:37.572 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855741073720352  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:37:37.572 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855741073720352  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:37:37.572 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855741073720352  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:37.572 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855741073720352  [0;39m 请求参数: []
2025-09-12 10:37:37.572 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855741073720352  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:37:37.579 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855741073720352  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:37:37.579 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855741073720352  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:37:37.579 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855741073720352  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:37:37.579 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855741073720352  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:37:37.590 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855741074310176  [0;39m ------------- 开始 -------------
2025-09-12 10:37:37.590 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855741074310176  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:37:37.590 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855741074310176  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:37:37.590 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855741074310176  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:37.590 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855741074310176  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:37:37.600 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855741074310176  [0;39m 总行数：2
2025-09-12 10:37:37.600 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855741074310176  [0;39m 总页数：1
2025-09-12 10:37:37.600 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855741074310176  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:37:37.600 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855741074310176  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 10:37:53.766 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855741604365344  [0;39m ------------- 开始 -------------
2025-09-12 10:37:53.769 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855741604365344  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:37:53.769 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855741604365344  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:37:53.769 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855741604365344  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:53.769 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855741604365344  [0;39m 请求参数: []
2025-09-12 10:37:53.769 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855741604365344  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:37:53.779 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855741604365344  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:37:53.779 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855741604365344  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:37:53.779 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855741604365344  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:37:53.779 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855741604365344  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-12 10:37:53.783 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855741604922400  [0;39m ------------- 开始 -------------
2025-09-12 10:37:53.783 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855741604922400  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:37:53.783 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855741604922400  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:37:53.783 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855741604922400  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:37:53.783 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855741604922400  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:37:53.797 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855741604922400  [0;39m 总行数：2
2025-09-12 10:37:53.797 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855741604922400  [0;39m 总页数：1
2025-09-12 10:37:53.797 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855741604922400  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:37:53.797 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855741604922400  [0;39m ------------- 结束 耗时：14 ms -------------
2025-09-12 10:38:22.765 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855742554604576  [0;39m ------------- 开始 -------------
2025-09-12 10:38:22.765 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855742554604576  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:38:22.765 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855742554604576  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:38:22.765 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855742554604576  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:38:22.765 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855742554604576  [0;39m 请求参数: []
2025-09-12 10:38:22.765 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855742554604576  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:38:22.779 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855742554604576  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:38:22.779 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855742554604576  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:38:22.779 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855742554604576  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:38:22.779 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855742554604576  [0;39m ------------- 结束 耗时：14 ms -------------
2025-09-12 10:38:22.787 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855742555325472  [0;39m ------------- 开始 -------------
2025-09-12 10:38:22.787 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855742555325472  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:38:22.787 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855742555325472  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:38:22.787 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855742555325472  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:38:22.787 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855742555325472  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:38:22.803 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855742555325472  [0;39m 总行数：2
2025-09-12 10:38:22.804 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855742555325472  [0;39m 总页数：1
2025-09-12 10:38:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855742555325472  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:38:22.804 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855742555325472  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 10:38:39.902 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855743116149792  [0;39m ------------- 开始 -------------
2025-09-12 10:38:39.902 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855743116149792  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:38:39.902 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855743116149792  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:38:39.905 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855743116149792  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:38:39.905 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855743116149792  [0;39m 请求参数: []
2025-09-12 10:38:39.905 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855743116149792  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:38:39.909 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855743116149792  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:38:39.909 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855743116149792  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:38:39.909 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855743116149792  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:38:39.909 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855743116149792  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:38:39.916 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855743116608544  [0;39m ------------- 开始 -------------
2025-09-12 10:38:39.916 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855743116608544  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:38:39.916 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855743116608544  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:38:39.916 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855743116608544  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:38:39.923 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855743116608544  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:38:39.931 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855743116608544  [0;39m 总行数：2
2025-09-12 10:38:39.931 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855743116608544  [0;39m 总页数：1
2025-09-12 10:38:39.933 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855743116608544  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:38:39.933 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855743116608544  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 10:39:19.568 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855744415925280  [0;39m ------------- 开始 -------------
2025-09-12 10:39:19.568 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855744415925280  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:39:19.568 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855744415925280  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:39:19.568 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855744415925280  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:39:19.569 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855744415925280  [0;39m 请求参数: []
2025-09-12 10:39:19.569 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855744415925280  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:39:19.574 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855744415925280  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:39:19.574 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855744415925280  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:39:19.574 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855744415925280  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:39:19.574 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855744415925280  [0;39m ------------- 结束 耗时：6 ms -------------
2025-09-12 10:39:19.582 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855744416384032  [0;39m ------------- 开始 -------------
2025-09-12 10:39:19.584 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855744416384032  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:39:19.584 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855744416384032  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:39:19.584 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855744416384032  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:39:19.584 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855744416384032  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:39:19.590 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855744416384032  [0;39m 总行数：2
2025-09-12 10:39:19.590 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855744416384032  [0;39m 总页数：1
2025-09-12 10:39:19.597 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855744416384032  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:39:19.597 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855744416384032  [0;39m ------------- 结束 耗时：15 ms -------------
2025-09-12 10:39:34.722 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855744912491552  [0;39m ------------- 开始 -------------
2025-09-12 10:39:34.722 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855744912491552  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:39:34.722 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855744912491552  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:39:34.722 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855744912491552  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:39:34.724 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855744912491552  [0;39m 请求参数: []
2025-09-12 10:39:34.724 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855744912491552  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:39:34.741 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855744912491552  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:39:34.743 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855744912491552  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:39:34.743 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855744912491552  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:39:34.745 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855744912491552  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-12 10:39:34.767 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855744913966112  [0;39m ------------- 开始 -------------
2025-09-12 10:39:34.767 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855744913966112  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:39:34.767 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855744913966112  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:39:34.767 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855744913966112  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:39:34.770 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855744913966112  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:39:34.801 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855744913966112  [0;39m 总行数：2
2025-09-12 10:39:34.808 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855744913966112  [0;39m 总页数：1
2025-09-12 10:39:34.811 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855744913966112  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:39:34.814 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855744913966112  [0;39m ------------- 结束 耗时：47 ms -------------
2025-09-12 10:41:06.041 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-12 10:41:06.045 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-12 10:41:15.805 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 22240 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 10:41:15.809 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 10:41:16.682 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 10:41:16.685 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 10:41:16.722 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-09-12 10:41:17.446 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 10:41:17.456 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 10:41:17.457 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 10:41:17.458 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 10:41:17.558 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 10:41:17.558 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1701 ms
2025-09-12 10:41:19.988 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 10:41:20.421 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 10:41:20.445 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 10:41:20.458 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.127 seconds (JVM running for 6.849)
2025-09-12 10:41:20.462 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 10:41:20.462 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 10:43:12.940 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 10:43:12.940 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 10:43:12.944 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 4 ms
2025-09-12 10:43:13.021 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855752065713184  [0;39m ------------- 开始 -------------
2025-09-12 10:43:13.021 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855752065713185  [0;39m ------------- 开始 -------------
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855752065713185  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855752065713184  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855752065713184  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855752065713185  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855752065713185  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:43:13.029 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855752065713184  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:43:13.127 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855752065713184  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:43:13.127 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855752065713185  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:43:13.136 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855752065713185  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:43:13.136 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855752065713184  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:43:13.144 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855752065713184  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:43:13.144 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855752065713185  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:43:13.152 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855752065713185  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:43:13.152 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855752065713184  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:43:13.268 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4855752065713185  [0;39m {dataSource-1} inited
2025-09-12 10:43:13.432 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855752065713184  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:43:13.432 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855752065713185  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:43:13.459 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855752065713184  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:43:13.459 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855752065713184  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:43:13.459 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855752065713185  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:43:13.464 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855752065713185  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:43:13.473 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855752065713184  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:43:13.473 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855752065713185  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:43:13.473 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855752065713185  [0;39m ------------- 结束 耗时：452 ms -------------
2025-09-12 10:43:13.473 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855752065713184  [0;39m ------------- 结束 耗时：452 ms -------------
2025-09-12 10:43:15.486 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855752146486304  [0;39m ------------- 开始 -------------
2025-09-12 10:43:15.486 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855752146486304  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:43:15.486 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855752146486304  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:43:15.486 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855752146486304  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:43:15.486 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855752146486304  [0;39m 请求参数: []
2025-09-12 10:43:15.488 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855752146486304  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:43:15.501 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855752146486304  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:43:15.503 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855752146486304  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:43:15.512 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855752146486304  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:43:15.512 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855752146486304  [0;39m ------------- 结束 耗时：26 ms -------------
2025-09-12 10:43:15.540 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855752148255776  [0;39m ------------- 开始 -------------
2025-09-12 10:43:15.540 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855752148255776  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:43:15.540 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855752148255776  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:43:15.540 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855752148255776  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:43:15.545 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855752148255776  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:43:15.562 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855752148255776  [0;39m 总行数：2
2025-09-12 10:43:15.562 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855752148255776  [0;39m 总页数：1
2025-09-12 10:43:15.570 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855752148255776  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:43:15.570 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855752148255776  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-12 10:43:28.225 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855752563917856  [0;39m ------------- 开始 -------------
2025-09-12 10:43:28.225 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855752563917856  [0;39m 请求地址: http://localhost:8880/service/getAllServiceList GET
2025-09-12 10:43:28.226 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855752563917856  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getAllServiceList
2025-09-12 10:43:28.226 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855752563917856  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:43:28.226 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855752563917856  [0;39m 请求参数: [{"shopId":6,"status":1}]
2025-09-12 10:43:28.226 INFO  com.gec.wiki.controller.ServiceController         :69   [32m4855752563917856  [0;39m 查询所有服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=1, isRecommend=null, shopId=6, page=null, size=null}
2025-09-12 10:43:28.245 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855752563917856  [0;39m 返回结果: {"content":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"}],"message":"查询成功","success":true}
2025-09-12 10:43:28.245 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855752563917856  [0;39m ------------- 结束 耗时：20 ms -------------
2025-09-12 10:47:05.182 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855759673164832  [0;39m ------------- 开始 -------------
2025-09-12 10:47:05.184 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855759673164832  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:47:05.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855759673164832  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:47:05.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855759673164832  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:47:05.186 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855759673164832  [0;39m 请求参数: []
2025-09-12 10:47:05.186 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855759673164832  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:47:05.241 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855759673164832  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 216962
2025-09-12 10:47:05.247 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855759673164832  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 231788
2025-09-12 10:47:05.282 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855759673164832  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:47:05.284 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855759673164832  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:47:05.284 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855759673164832  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:47:05.284 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855759673164832  [0;39m ------------- 结束 耗时：102 ms -------------
2025-09-12 10:47:05.314 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855759677490208  [0;39m ------------- 开始 -------------
2025-09-12 10:47:05.316 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855759677490208  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:47:05.316 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855759677490208  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:47:05.316 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855759677490208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:47:05.316 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855759677490208  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:47:05.341 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855759677490208  [0;39m 总行数：2
2025-09-12 10:47:05.341 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855759677490208  [0;39m 总页数：1
2025-09-12 10:47:05.341 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855759677490208  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:47:05.341 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855759677490208  [0;39m ------------- 结束 耗时：27 ms -------------
2025-09-12 10:47:21.815 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855760218194976  [0;39m ------------- 开始 -------------
2025-09-12 10:47:21.817 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855760218194976  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:47:21.819 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855760218194976  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:47:21.819 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855760218194976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:47:21.819 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855760218194976  [0;39m 请求参数: []
2025-09-12 10:47:21.819 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855760218194976  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:47:21.831 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855760218194976  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:47:21.831 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855760218194976  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:47:21.831 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855760218194976  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:47:21.831 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855760218194976  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 10:47:21.854 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855760219472928  [0;39m ------------- 开始 -------------
2025-09-12 10:47:21.855 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855760219472928  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:47:21.855 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855760219472928  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:47:21.855 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855760219472928  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:47:21.855 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855760219472928  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:47:21.879 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855760219472928  [0;39m 总行数：2
2025-09-12 10:47:21.880 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855760219472928  [0;39m 总页数：1
2025-09-12 10:47:21.880 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855760219472928  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:47:21.880 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855760219472928  [0;39m ------------- 结束 耗时：26 ms -------------
2025-09-12 10:50:46.516 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855766925837344  [0;39m ------------- 开始 -------------
2025-09-12 10:50:46.516 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855766925837344  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:50:46.516 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855766925837344  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:50:46.516 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855766925837345  [0;39m ------------- 开始 -------------
2025-09-12 10:50:46.517 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855766925837345  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:50:46.517 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855766925837344  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:50:46.518 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855766925837345  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:50:46.518 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855766925837344  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:50:46.518 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855766925837345  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:50:46.518 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855766925837344  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:50:46.518 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855766925837345  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:50:46.518 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855766925837344  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:50:46.518 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855766925837345  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:50:46.518 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855766925837344  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:50:46.518 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855766925837345  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:50:46.518 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855766925837345  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:50:46.524 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855766925837345  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 204648
2025-09-12 10:50:46.538 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855766925837344  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:50:46.545 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855766925837345  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:50:46.549 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855766925837344  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:50:46.549 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855766925837344  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:50:46.550 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855766925837344  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:50:46.550 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855766925837344  [0;39m ------------- 结束 耗时：34 ms -------------
2025-09-12 10:50:46.553 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855766925837345  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:50:46.555 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855766925837345  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:50:46.555 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855766925837345  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:50:46.555 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855766925837345  [0;39m ------------- 结束 耗时：39 ms -------------
2025-09-12 10:50:48.546 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855766992356384  [0;39m ------------- 开始 -------------
2025-09-12 10:50:48.546 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855766992356384  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:50:48.546 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855766992356384  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:50:48.546 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855766992356384  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:50:48.546 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855766992356384  [0;39m 请求参数: []
2025-09-12 10:50:48.546 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855766992356384  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:50:48.559 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855766992356384  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:50:48.559 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855766992356384  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:50:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855766992356384  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:50:48.559 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855766992356384  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-12 10:50:48.602 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855766994191392  [0;39m ------------- 开始 -------------
2025-09-12 10:50:48.602 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855766994191392  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:50:48.602 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855766994191392  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:50:48.602 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855766994191392  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:50:48.602 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855766994191392  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:50:48.615 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855766994191392  [0;39m 总行数：2
2025-09-12 10:50:48.615 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855766994191392  [0;39m 总页数：1
2025-09-12 10:50:48.615 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855766994191392  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:50:48.615 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855766994191392  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-12 10:50:58.065 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855767304274976  [0;39m ------------- 开始 -------------
2025-09-12 10:50:58.067 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855767304274976  [0;39m 请求地址: http://localhost:8880/service/getAllServiceList GET
2025-09-12 10:50:58.067 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855767304274976  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getAllServiceList
2025-09-12 10:50:58.067 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855767304274976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:50:58.069 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855767304274976  [0;39m 请求参数: [{"shopId":7,"status":1}]
2025-09-12 10:50:58.069 INFO  com.gec.wiki.controller.ServiceController         :69   [32m4855767304274976  [0;39m 查询所有服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=1, isRecommend=null, shopId=7, page=null, size=null}
2025-09-12 10:50:58.075 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855767304274976  [0;39m 返回结果: {"content":[],"message":"查询成功","success":true}
2025-09-12 10:50:58.075 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855767304274976  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 10:51:00.304 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855767377642528  [0;39m ------------- 开始 -------------
2025-09-12 10:51:00.304 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855767377642528  [0;39m 请求地址: http://localhost:8880/service/getAllServiceList GET
2025-09-12 10:51:00.304 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855767377642528  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getAllServiceList
2025-09-12 10:51:00.304 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855767377642528  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:51:00.304 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855767377642528  [0;39m 请求参数: [{"shopId":6,"status":1}]
2025-09-12 10:51:00.304 INFO  com.gec.wiki.controller.ServiceController         :69   [32m4855767377642528  [0;39m 查询所有服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=1, isRecommend=null, shopId=6, page=null, size=null}
2025-09-12 10:51:00.311 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855767377642528  [0;39m 返回结果: {"content":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"}],"message":"查询成功","success":true}
2025-09-12 10:51:00.311 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855767377642528  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:52:06.538 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855769547998240  [0;39m ------------- 开始 -------------
2025-09-12 10:52:06.538 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855769547998240  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:06.538 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855769547998240  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:06.538 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855769547998240  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:06.540 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855769547998240  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-12 10:52:06.545 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855769547998240  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 66234
2025-09-12 10:52:06.549 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855769547998240  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 80000
2025-09-12 10:52:06.568 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855769547998240  [0;39m 总行数：2
2025-09-12 10:52:06.568 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855769547998240  [0;39m 总页数：1
2025-09-12 10:52:06.568 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855769547998240  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-11T22:54:39","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:52:06.568 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855769547998240  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-12 10:52:09.812 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855769655280672  [0;39m ------------- 开始 -------------
2025-09-12 10:52:09.815 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855769655280672  [0;39m 请求地址: http://localhost:8880/vehicle/save POST
2025-09-12 10:52:09.815 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855769655280672  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.save
2025-09-12 10:52:09.815 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855769655280672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:09.819 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855769655280672  [0;39m 请求参数: [{"brand":"宝马","color":"蓝色","engineNumber":"2555","id":****************,"isDefault":1,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"userId":5,"vin":"6666","year":2022}]
2025-09-12 10:52:09.819 INFO  com.gec.wiki.controller.VehicleController         :66   [32m4855769655280672  [0;39m 收到的保存请求：ID = ****************, 车牌号 = 6666, 用户ID = 5
2025-09-12 10:52:09.826 INFO  com.gec.wiki.controller.VehicleController         :109  [32m4855769655280672  [0;39m 更新ID = ****************
2025-09-12 10:52:09.879 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855769655280672  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-09-12 10:52:09.881 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855769655280672  [0;39m ------------- 结束 耗时：69 ms -------------
2025-09-12 10:52:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855769658393632  [0;39m ------------- 开始 -------------
2025-09-12 10:52:09.907 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855769658393632  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:09.912 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855769658393632  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:09.912 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855769658393632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:09.913 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855769658393632  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-12 10:52:09.923 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855769658393632  [0;39m 总行数：2
2025-09-12 10:52:09.924 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855769658393632  [0;39m 总页数：1
2025-09-12 10:52:09.924 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855769658393632  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":1,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":0,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:52:09.924 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855769658393632  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 10:52:29.701 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855770307003424  [0;39m ------------- 开始 -------------
2025-09-12 10:52:29.702 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855770307003424  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:29.702 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855770307003424  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:29.702 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855770307003424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:29.702 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855770307003424  [0;39m 请求参数: [{"brand":"宝马","page":1,"size":10,"userId":5}]
2025-09-12 10:52:29.720 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855770307003424  [0;39m 总行数：1
2025-09-12 10:52:29.722 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855770307003424  [0;39m 总页数：1
2025-09-12 10:52:29.722 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855770307003424  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":1,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022}],"total":1},"success":true}
2025-09-12 10:52:29.722 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855770307003424  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-12 10:52:45.326 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855770819003424  [0;39m ------------- 开始 -------------
2025-09-12 10:52:45.326 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855770819003424  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:52:45.326 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855770819003424  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:52:45.326 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855770819003424  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:45.326 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855770819003424  [0;39m 请求参数: []
2025-09-12 10:52:45.326 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855770819003424  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:52:45.347 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855770819003424  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:52:45.347 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855770819003424  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:52:45.347 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855770819003424  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:52:45.347 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855770819003424  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-12 10:52:45.420 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855770822083616  [0;39m ------------- 开始 -------------
2025-09-12 10:52:45.420 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855770822083616  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:45.420 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855770822083616  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:45.424 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855770822083616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:45.424 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855770822083616  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:52:45.446 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855770822083616  [0;39m 总行数：2
2025-09-12 10:52:45.446 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855770822083616  [0;39m 总页数：1
2025-09-12 10:52:45.452 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855770822083616  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":1,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":0,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:52:45.452 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855770822083616  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-12 10:52:49.963 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855770970948640  [0;39m ------------- 开始 -------------
2025-09-12 10:52:49.963 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855770970948640  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:49.968 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855770970948640  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:49.968 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855770970948640  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:49.968 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855770970948640  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-12 10:52:49.982 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855770970948640  [0;39m 总行数：2
2025-09-12 10:52:49.984 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855770970948640  [0;39m 总页数：1
2025-09-12 10:52:49.988 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855770970948640  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":1,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":0,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-11T22:56:22","userId":5}],"total":2},"success":true}
2025-09-12 10:52:49.991 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855770970948640  [0;39m ------------- 结束 耗时：28 ms -------------
2025-09-12 10:52:53.424 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771084358688  [0;39m ------------- 开始 -------------
2025-09-12 10:52:53.424 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771084358688  [0;39m 请求地址: http://localhost:8880/vehicle/save POST
2025-09-12 10:52:53.424 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771084358688  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.save
2025-09-12 10:52:53.424 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771084358688  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:53.430 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771084358688  [0;39m 请求参数: [{"brand":"小米","color":"蓝色","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"userId":5}]
2025-09-12 10:52:53.430 INFO  com.gec.wiki.controller.VehicleController         :66   [32m4855771084358688  [0;39m 收到的保存请求：ID = 4850817489077287, 车牌号 = 8888, 用户ID = 5
2025-09-12 10:52:53.430 INFO  com.gec.wiki.controller.VehicleController         :109  [32m4855771084358688  [0;39m 更新ID = 4850817489077287
2025-09-12 10:52:53.446 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771084358688  [0;39m 返回结果: {"message":"修改成功","success":true}
2025-09-12 10:52:53.446 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771084358688  [0;39m ------------- 结束 耗时：22 ms -------------
2025-09-12 10:52:53.470 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771085866016  [0;39m ------------- 开始 -------------
2025-09-12 10:52:53.470 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771085866016  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:53.470 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771085866016  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:53.470 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771085866016  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:53.470 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771085866016  [0;39m 请求参数: [{"page":1,"size":10,"userId":5}]
2025-09-12 10:52:53.495 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855771085866016  [0;39m 总行数：2
2025-09-12 10:52:53.495 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855771085866016  [0;39m 总页数：1
2025-09-12 10:52:53.497 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771085866016  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-12T10:52:53","userId":5}],"total":2},"success":true}
2025-09-12 10:52:53.498 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771085866016  [0;39m ------------- 结束 耗时：28 ms -------------
2025-09-12 10:52:55.172 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771141637152  [0;39m ------------- 开始 -------------
2025-09-12 10:52:55.172 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771141637152  [0;39m 请求地址: http://localhost:8880/shop/list GET
2025-09-12 10:52:55.172 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771141637152  [0;39m 类名方法: com.gec.wiki.controller.ShopController.getAllShops
2025-09-12 10:52:55.172 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771141637152  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:55.172 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771141637152  [0;39m 请求参数: []
2025-09-12 10:52:55.172 INFO  com.gec.wiki.controller.ShopController            :85   [32m4855771141637152  [0;39m 🏪 获取所有维修店列表
2025-09-12 10:52:55.180 INFO  com.gec.wiki.service.impl.ShopServiceImpl         :52   [32m4855771141637152  [0;39m 获取到 4 家营业中的维修店
2025-09-12 10:52:55.180 INFO  com.gec.wiki.controller.ShopController            :106  [32m4855771141637152  [0;39m ✅ 获取到 4 家维修店
2025-09-12 10:52:55.180 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771141637152  [0;39m 返回结果: {"content":[{"phone":"***********","name":"1997","rating":4.5,"businessHours":"09:00-18:00","id":12},{"phone":"***********","name":"123","rating":4.5,"businessHours":"09:00-18:00","id":7},{"phone":"***********","name":"小石头的店铺","rating":4.5,"businessHours":"09:00-18:00","id":6},{"phone":"***********","name":"111","rating":4.5,"businessHours":"09:00-18:00","id":4}],"message":"查询成功","success":true}
2025-09-12 10:52:55.180 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771141637152  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 10:52:55.220 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771143210016  [0;39m ------------- 开始 -------------
2025-09-12 10:52:55.220 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771143210016  [0;39m 请求地址: http://localhost:8880/vehicle/getVehicleListByPage GET
2025-09-12 10:52:55.222 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771143210016  [0;39m 类名方法: com.gec.wiki.controller.VehicleController.getVehicleListByPage
2025-09-12 10:52:55.222 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771143210016  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:52:55.222 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771143210016  [0;39m 请求参数: [{"page":1,"size":100,"userId":5}]
2025-09-12 10:52:55.229 INFO  com.gec.wiki.controller.VehicleController         :48   [32m4855771143210016  [0;39m 总行数：2
2025-09-12 10:52:55.229 INFO  com.gec.wiki.controller.VehicleController         :49   [32m4855771143210016  [0;39m 总页数：1
2025-09-12 10:52:55.234 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771143210016  [0;39m 返回结果: {"content":{"list":[{"brand":"宝马","color":"蓝色","createTime":"2025-09-11T12:10:44","engineNumber":"2555","id":****************,"isDefault":0,"licensePlate":"6666","mileage":6000,"model":"宝马X1","status":1,"updateTime":"2025-09-12T10:52:10","userId":5,"vin":"6666","year":2022},{"brand":"小米","color":"蓝色","createTime":"2025-09-11T12:58:12","id":4850817489077287,"isDefault":1,"licensePlate":"8888","mileage":0,"model":"小米SU7","status":1,"updateTime":"2025-09-12T10:52:53","userId":5}],"total":2},"success":true}
2025-09-12 10:52:55.235 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771143210016  [0;39m ------------- 结束 耗时：15 ms -------------
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771675362336  [0;39m ------------- 开始 -------------
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771675362337  [0;39m ------------- 开始 -------------
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771675362336  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771675362337  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771675362336  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:53:11.460 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771675362337  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:53:11.464 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771675362336  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:11.464 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771675362337  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:11.464 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771675362337  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:53:11.464 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771675362336  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:53:11.464 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855771675362337  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:53:11.464 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855771675362336  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:53:11.464 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855771675362337  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:53:11.464 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855771675362336  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:53:11.464 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855771675362337  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:53:11.464 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855771675362336  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:53:11.485 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855771675362336  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:53:11.487 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855771675362337  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:53:11.769 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855771675362336  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:53:11.769 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855771675362336  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:53:11.774 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855771675362337  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:53:11.774 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771675362336  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:53:11.774 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771675362336  [0;39m ------------- 结束 耗时：314 ms -------------
2025-09-12 10:53:11.774 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855771675362337  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:53:11.774 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771675362337  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:53:11.774 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771675362337  [0;39m ------------- 结束 耗时：314 ms -------------
2025-09-12 10:53:20.689 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771977778208  [0;39m ------------- 开始 -------------
2025-09-12 10:53:20.689 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771977778208  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-12 10:53:20.689 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771977778208  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-12 10:53:20.689 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771977778208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:20.689 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771977778208  [0;39m 请求参数: [{"username":"1997"}]
2025-09-12 10:53:20.696 INFO  com.gec.wiki.service.impl.UserServiceImpl         :69   [32m4855771977778208  [0;39m 🔐 用户登录尝试：username=1997, ip=0:0:0:0:0:0:0:1
2025-09-12 10:53:20.696 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4855771977778208  [0;39m 🔒 检查用户锁定状态：username=1997
2025-09-12 10:53:20.716 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :65   [32m4855771977778208  [0;39m ✅ 用户未被锁定：username=1997
2025-09-12 10:53:20.842 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4855771977778208  [0;39m 🧹 清除登录失败记录：username=1997
2025-09-12 10:53:20.857 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4855771977778208  [0;39m ✅ 成功清除登录失败记录：username=1997, 清除记录数=1
2025-09-12 10:53:20.864 INFO  com.gec.wiki.utils.TokenManager                   :32   [32m4855771977778208  [0;39m 存储token成功: userId=12, username=1997
2025-09-12 10:53:20.864 INFO  com.gec.wiki.service.impl.UserServiceImpl         :169  [32m4855771977778208  [0;39m ✅ 用户登录成功：username=1997, userType=2, ip=0:0:0:0:0:0:0:1
2025-09-12 10:53:20.866 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771977778208  [0;39m 返回结果: {"content":{"userInfo":{"realName":"牛皮","phone":"***********","id":12,"userType":2,"email":"","username":"1997"},"token":"67e6c3213d0f426e853a58903a7e85d0"},"message":"登录成功","success":true}
2025-09-12 10:53:20.867 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771977778208  [0;39m ------------- 结束 耗时：178 ms -------------
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771987084320  [0;39m ------------- 开始 -------------
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855771987084321  [0;39m ------------- 开始 -------------
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771987084321  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855771987084320  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771987084321  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855771987084320  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771987084321  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855771987084320  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771987084321  [0;39m 请求参数: []
2025-09-12 10:53:20.973 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855771987084320  [0;39m 请求参数: []
2025-09-12 10:53:20.982 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855771987084321  [0;39m 📊 获取维修店预约统计
2025-09-12 10:53:20.982 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855771987084320  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 10:53:20.990 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855771987084320  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 10:53:20.990 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771987084320  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 10:53:20.990 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771987084320  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 10:53:20.998 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855771987084321  [0;39m 获取维修店1预约统计成功
2025-09-12 10:53:20.998 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855771987084321  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 10:53:20.998 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855771987084321  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-12 10:53:23.436 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772067791904  [0;39m ------------- 开始 -------------
2025-09-12 10:53:23.441 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772067791904  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 10:53:23.441 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772067791904  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 10:53:23.441 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772067791904  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:23.441 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772067791904  [0;39m 请求参数: []
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772068054048  [0;39m ------------- 开始 -------------
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772068054048  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772068054048  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772068054048  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772068054048  [0;39m 请求参数: [1,10,""]
2025-09-12 10:53:23.444 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855772067791904  [0;39m 获取维修店1预约统计成功
2025-09-12 10:53:23.444 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855772067791904  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772067791904  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 10:53:23.444 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772067791904  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 10:53:23.466 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855772068054048  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 10:53:23.466 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855772068054048  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 10:53:23.466 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772068054048  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 10:53:23.469 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772068054048  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772336358432  [0;39m ------------- 开始 -------------
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772336358432  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772336358433  [0;39m ------------- 开始 -------------
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772336358432  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772336358433  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772336358432  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772336358433  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772336358432  [0;39m 请求参数: []
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772336358433  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:31.632 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772336358433  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 10:53:31.638 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4855772336358433  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 10:53:31.638 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4855772336358432  [0;39m 📋 获取服务分类列表
2025-09-12 10:53:31.640 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :336  [32m4855772336358433  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}, shopId: 12
2025-09-12 10:53:31.641 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :382  [32m4855772336358433  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-12 10:53:31.641 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4855772336358432  [0;39m 🌲 构建分类树形结构
2025-09-12 10:53:31.648 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :386  [32m4855772336358433  [0;39m 📋 维修店 12 查询结果：共 0 条记录，当前页 0 条
2025-09-12 10:53:31.648 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :402  [32m4855772336358433  [0;39m ✅ 维修店 12 服务查询完成，返回 0 条记录
2025-09-12 10:53:31.648 INFO  com.gec.wiki.controller.ShopServiceController     :74   [32m4855772336358433  [0;39m ✅ 维修店 12 查询到 0 条服务记录
2025-09-12 10:53:31.649 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772336358433  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"查询成功","success":true}
2025-09-12 10:53:31.650 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772336358433  [0;39m ------------- 结束 耗时：18 ms -------------
2025-09-12 10:53:31.661 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4855772336358432  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 10:53:31.666 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772336358432  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 10:53:31.666 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772336358432  [0;39m ------------- 结束 耗时：34 ms -------------
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772580774944  [0;39m ------------- 开始 -------------
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855772580774945  [0;39m ------------- 开始 -------------
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772580774945  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855772580774944  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772580774945  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855772580774944  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772580774945  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855772580774944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772580774945  [0;39m 请求参数: []
2025-09-12 10:53:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855772580774944  [0;39m 请求参数: [1,10,""]
2025-09-12 10:53:39.096 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855772580774945  [0;39m 获取维修店1预约统计成功
2025-09-12 10:53:39.099 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855772580774945  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 10:53:39.099 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772580774945  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 10:53:39.099 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772580774945  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 10:53:39.105 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855772580774944  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 10:53:39.105 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855772580774944  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 10:53:39.105 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855772580774944  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 10:53:39.107 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855772580774944  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855774343037984  [0;39m ------------- 开始 -------------
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855774343037985  [0;39m ------------- 开始 -------------
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855774343037984  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855774343037984  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855774343037985  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855774343037984  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855774343037985  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855774343037985  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:54:32.871 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855774343037984  [0;39m 请求参数: []
2025-09-12 10:54:32.873 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855774343037985  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 10:54:32.873 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4855774343037984  [0;39m 📋 获取服务分类列表
2025-09-12 10:54:32.873 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4855774343037985  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 10:54:32.873 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4855774343037984  [0;39m 🌲 构建分类树形结构
2025-09-12 10:54:32.873 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :336  [32m4855774343037985  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}, shopId: 12
2025-09-12 10:54:32.873 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :382  [32m4855774343037985  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-12 10:54:32.879 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :386  [32m4855774343037985  [0;39m 📋 维修店 12 查询结果：共 0 条记录，当前页 0 条
2025-09-12 10:54:32.879 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :402  [32m4855774343037985  [0;39m ✅ 维修店 12 服务查询完成，返回 0 条记录
2025-09-12 10:54:32.879 INFO  com.gec.wiki.controller.ShopServiceController     :74   [32m4855774343037985  [0;39m ✅ 维修店 12 查询到 0 条服务记录
2025-09-12 10:54:32.879 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4855774343037984  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 10:54:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855774343037985  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"查询成功","success":true}
2025-09-12 10:54:32.881 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855774343037984  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 10:54:32.882 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855774343037984  [0;39m ------------- 结束 耗时：11 ms -------------
2025-09-12 10:54:32.882 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855774343037985  [0;39m ------------- 结束 耗时：11 ms -------------
2025-09-12 10:54:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855774705878049  [0;39m ------------- 开始 -------------
2025-09-12 10:54:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855774705878049  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 10:54:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855774705878048  [0;39m ------------- 开始 -------------
2025-09-12 10:54:43.945 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855774705878048  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 10:54:43.944 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855774705878049  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 10:54:43.945 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855774705878048  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855774705878048  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:54:43.945 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855774705878049  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855774705878048  [0;39m 请求参数: []
2025-09-12 10:54:43.946 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855774705878048  [0;39m 📊 获取维修店预约统计
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855774705878049  [0;39m 请求参数: []
2025-09-12 10:54:43.946 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855774705878049  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 10:54:43.946 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855774705878048  [0;39m 获取维修店1预约统计成功
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855774705878048  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855774705878048  [0;39m ------------- 结束 耗时：2 ms -------------
2025-09-12 10:54:43.946 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855774705878049  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855774705878049  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 10:54:43.946 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855774705878049  [0;39m ------------- 结束 耗时：2 ms -------------
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775687607328  [0;39m ------------- 开始 -------------
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775687607328  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775687607328  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775687607329  [0;39m ------------- 开始 -------------
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775687607329  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775687607328  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775687607329  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775687607328  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775687607329  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:13.904 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855775687607328  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:55:13.904 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775687607329  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:55:13.904 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855775687607328  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:55:13.904 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855775687607329  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:55:13.904 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855775687607328  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:55:13.904 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855775687607329  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:55:13.904 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855775687607329  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:55:13.918 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855775687607329  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:55:13.918 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855775687607328  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:55:13.925 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855775687607329  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:55:13.925 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855775687607328  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:55:13.925 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855775687607329  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:55:13.925 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855775687607328  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:55:13.925 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775687607329  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:55:13.925 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775687607328  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:55:13.925 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775687607329  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-12 10:55:13.928 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775687607328  [0;39m ------------- 结束 耗时：24 ms -------------
2025-09-12 10:55:18.043 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775823234080  [0;39m ------------- 开始 -------------
2025-09-12 10:55:18.043 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775823234080  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-12 10:55:18.043 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775823234080  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-12 10:55:18.045 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775823234080  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:18.045 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775823234080  [0;39m 请求参数: [{"username":"小石头的店铺"}]
2025-09-12 10:55:18.053 INFO  com.gec.wiki.service.impl.UserServiceImpl         :69   [32m4855775823234080  [0;39m 🔐 用户登录尝试：username=小石头的店铺, ip=0:0:0:0:0:0:0:1
2025-09-12 10:55:18.053 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4855775823234080  [0;39m 🔒 检查用户锁定状态：username=小石头的店铺
2025-09-12 10:55:18.055 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :58   [32m4855775823234080  [0;39m ⏰ 用户锁定已过期，清除锁定时间但保留失败记录：username=小石头的店铺
2025-09-12 10:55:18.055 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :131  [32m4855775823234080  [0;39m 🧹 清除锁定时间：username=小石头的店铺
2025-09-12 10:55:18.067 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :142  [32m4855775823234080  [0;39m ✅ 成功清除锁定时间：username=小石头的店铺, 更新记录数=1
2025-09-12 10:55:18.191 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4855775823234080  [0;39m 🧹 清除登录失败记录：username=小石头的店铺
2025-09-12 10:55:18.199 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4855775823234080  [0;39m ✅ 成功清除登录失败记录：username=小石头的店铺, 清除记录数=1
2025-09-12 10:55:18.203 INFO  com.gec.wiki.utils.TokenManager                   :32   [32m4855775823234080  [0;39m 存储token成功: userId=6, username=小石头的店铺
2025-09-12 10:55:18.203 INFO  com.gec.wiki.service.impl.UserServiceImpl         :169  [32m4855775823234080  [0;39m ✅ 用户登录成功：username=小石头的店铺, userType=2, ip=0:0:0:0:0:0:0:1
2025-09-12 10:55:18.204 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775823234080  [0;39m 返回结果: {"content":{"userInfo":{"realName":"恶梦","phone":"***********","id":6,"userType":2,"email":"<EMAIL>","username":"小石头的店铺"},"token":"c7dedadda3bb4b63b13d4424d883744d"},"message":"登录成功","success":true}
2025-09-12 10:55:18.204 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775823234080  [0;39m ------------- 结束 耗时：161 ms -------------
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775831688224  [0;39m ------------- 开始 -------------
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775831688224  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775831688225  [0;39m ------------- 开始 -------------
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775831688224  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775831688225  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775831688224  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775831688225  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775831688225  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:18.301 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775831688224  [0;39m 请求参数: []
2025-09-12 10:55:18.305 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775831688225  [0;39m 请求参数: []
2025-09-12 10:55:18.305 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855775831688224  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 10:55:18.305 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855775831688225  [0;39m 📊 获取维修店预约统计
2025-09-12 10:55:18.308 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855775831688224  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 10:55:18.308 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775831688224  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 10:55:18.308 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775831688224  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:55:18.308 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855775831688225  [0;39m 获取维修店1预约统计成功
2025-09-12 10:55:18.308 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775831688225  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 10:55:18.308 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775831688225  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775880348704  [0;39m ------------- 开始 -------------
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775880348705  [0;39m ------------- 开始 -------------
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775880348704  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775880348704  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775880348705  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775880348704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775880348705  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775880348705  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775880348704  [0;39m 请求参数: []
2025-09-12 10:55:19.786 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775880348705  [0;39m 请求参数: [1,10,""]
2025-09-12 10:55:19.786 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855775880348704  [0;39m 获取维修店1预约统计成功
2025-09-12 10:55:19.793 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855775880348704  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 10:55:19.793 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775880348704  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 10:55:19.793 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775880348704  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:55:19.801 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855775880348705  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 10:55:19.803 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855775880348705  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 10:55:19.803 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775880348705  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 10:55:19.803 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775880348705  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 10:55:21.354 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775931728928  [0;39m ------------- 开始 -------------
2025-09-12 10:55:21.354 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855775931728929  [0;39m ------------- 开始 -------------
2025-09-12 10:55:21.354 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775931728928  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855775931728929  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775931728928  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855775931728929  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775931728928  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855775931728929  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775931728928  [0;39m 请求参数: []
2025-09-12 10:55:21.355 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855775931728929  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 10:55:21.355 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4855775931728928  [0;39m 📋 获取服务分类列表
2025-09-12 10:55:21.355 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4855775931728929  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 10:55:21.355 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4855775931728928  [0;39m 🌲 构建分类树形结构
2025-09-12 10:55:21.355 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :336  [32m4855775931728929  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}, shopId: 6
2025-09-12 10:55:21.355 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :382  [32m4855775931728929  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-12 10:55:21.358 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4855775931728928  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 10:55:21.358 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775931728928  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 10:55:21.362 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775931728928  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 10:55:21.367 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :386  [32m4855775931728929  [0;39m 📋 维修店 6 查询结果：共 1 条记录，当前页 1 条
2025-09-12 10:55:21.370 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :402  [32m4855775931728929  [0;39m ✅ 维修店 6 服务查询完成，返回 1 条记录
2025-09-12 10:55:21.370 INFO  com.gec.wiki.controller.ShopServiceController     :74   [32m4855775931728929  [0;39m ✅ 维修店 6 查询到 1 条服务记录
2025-09-12 10:55:21.370 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855775931728929  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"}],"total":1},"message":"查询成功","success":true}
2025-09-12 10:55:21.370 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855775931728929  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855780221256736  [0;39m ------------- 开始 -------------
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855780221256736  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855780221256736  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855780221256736  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855780221256737  [0;39m ------------- 开始 -------------
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855780221256737  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855780221256736  [0;39m 请求参数: [1,10,""]
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855780221256737  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855780221256737  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:57:32.260 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855780221256737  [0;39m 请求参数: []
2025-09-12 10:57:32.260 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855780221256736  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 130891
2025-09-12 10:57:32.260 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4855780221256737  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 132474
2025-09-12 10:57:32.292 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855780221256737  [0;39m 获取维修店1预约统计成功
2025-09-12 10:57:32.292 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855780221256737  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 10:57:32.292 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855780221256737  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 10:57:32.292 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855780221256737  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-12 10:57:32.292 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855780221256736  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 10:57:32.292 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855780221256736  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 10:57:32.292 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855780221256736  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 10:57:32.292 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855780221256736  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855781974344736  [0;39m ------------- 开始 -------------
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855781974344737  [0;39m ------------- 开始 -------------
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855781974344736  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855781974344737  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855781974344736  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855781974344737  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855781974344736  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855781974344737  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855781974344737  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 10:58:25.760 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855781974344736  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 10:58:25.763 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855781974344737  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:58:25.763 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855781974344736  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:58:25.763 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855781974344737  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 10:58:25.763 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855781974344736  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 10:58:25.763 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855781974344737  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 10:58:25.763 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855781974344736  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 10:58:25.769 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855781974344736  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:58:25.769 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855781974344737  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 10:58:25.776 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855781974344737  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:58:25.776 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855781974344736  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 10:58:25.776 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855781974344737  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:58:25.776 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855781974344736  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 10:58:25.776 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855781974344737  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:58:25.776 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855781974344736  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 10:58:25.776 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855781974344737  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 10:58:25.776 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855781974344736  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 10:58:29.952 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855782111708192  [0;39m ------------- 开始 -------------
2025-09-12 10:58:29.952 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855782111708192  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-12 10:58:29.952 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855782111708192  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-12 10:58:29.952 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855782111708192  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:29.952 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855782111708192  [0;39m 请求参数: [{"username":"1997"}]
2025-09-12 10:58:29.952 INFO  com.gec.wiki.service.impl.UserServiceImpl         :69   [32m4855782111708192  [0;39m 🔐 用户登录尝试：username=1997, ip=0:0:0:0:0:0:0:1
2025-09-12 10:58:29.959 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4855782111708192  [0;39m 🔒 检查用户锁定状态：username=1997
2025-09-12 10:58:29.960 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :65   [32m4855782111708192  [0;39m ✅ 用户未被锁定：username=1997
2025-09-12 10:58:30.092 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4855782111708192  [0;39m 🧹 清除登录失败记录：username=1997
2025-09-12 10:58:30.099 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4855782111708192  [0;39m ✅ 成功清除登录失败记录：username=1997, 清除记录数=1
2025-09-12 10:58:30.104 INFO  com.gec.wiki.utils.TokenManager                   :32   [32m4855782111708192  [0;39m 存储token成功: userId=12, username=1997
2025-09-12 10:58:30.104 INFO  com.gec.wiki.service.impl.UserServiceImpl         :169  [32m4855782111708192  [0;39m ✅ 用户登录成功：username=1997, userType=2, ip=0:0:0:0:0:0:0:1
2025-09-12 10:58:30.104 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855782111708192  [0;39m 返回结果: {"content":{"userInfo":{"realName":"牛皮","phone":"***********","id":12,"userType":2,"email":"","username":"1997"},"token":"43b84acf69e24fdf8119971420c9451d"},"message":"登录成功","success":true}
2025-09-12 10:58:30.106 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855782111708192  [0;39m ------------- 结束 耗时：154 ms -------------
2025-09-12 10:58:30.216 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855782120358944  [0;39m ------------- 开始 -------------
2025-09-12 10:58:30.216 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855782120358945  [0;39m ------------- 开始 -------------
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855782120358944  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855782120358945  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855782120358944  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855782120358945  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855782120358944  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855782120358945  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855782120358944  [0;39m 请求参数: []
2025-09-12 10:58:30.218 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855782120358945  [0;39m 请求参数: []
2025-09-12 10:58:30.218 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855782120358944  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 10:58:30.218 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855782120358945  [0;39m 📊 获取维修店预约统计
2025-09-12 10:58:30.223 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855782120358944  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 10:58:30.223 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855782120358945  [0;39m 获取维修店1预约统计成功
2025-09-12 10:58:30.223 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855782120358944  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 10:58:30.223 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855782120358944  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:58:30.223 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855782120358945  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 10:58:30.223 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855782120358945  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 10:58:32.512 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855782195594273  [0;39m ------------- 开始 -------------
2025-09-12 10:58:32.512 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855782195594272  [0;39m ------------- 开始 -------------
2025-09-12 10:58:32.512 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855782195594273  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855782195594273  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855782195594272  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855782195594272  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855782195594273  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855782195594272  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855782195594273  [0;39m 请求参数: [1,10,""]
2025-09-12 10:58:32.513 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855782195594272  [0;39m 请求参数: []
2025-09-12 10:58:32.513 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855782195594272  [0;39m 获取维修店1预约统计成功
2025-09-12 10:58:32.513 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855782195594272  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 10:58:32.517 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855782195594272  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 10:58:32.517 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855782195594272  [0;39m ------------- 结束 耗时：5 ms -------------
2025-09-12 10:58:32.520 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855782195594273  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 10:58:32.520 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855782195594273  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 10:58:32.520 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855782195594273  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 10:58:32.520 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855782195594273  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 11:09:58.586 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-12 11:09:58.593 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-12 11:10:07.608 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 28488 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 11:10:07.613 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 11:10:08.654 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 11:10:08.658 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 11:10:08.711 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-09-12 11:10:09.461 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 11:10:09.472 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 11:10:09.472 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 11:10:09.472 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 11:10:09.571 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 11:10:09.572 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1884 ms
2025-09-12 11:10:11.801 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 11:10:12.145 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 11:10:12.167 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 11:10:12.176 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.15 seconds (JVM running for 6.985)
2025-09-12 11:10:12.178 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 11:10:12.178 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 11:10:32.572 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 11:10:32.572 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 11:10:32.572 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-12 11:10:32.656 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805793272865  [0;39m ------------- 开始 -------------
2025-09-12 11:10:32.656 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805793272864  [0;39m ------------- 开始 -------------
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805793272864  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805793272865  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805793272865  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805793272864  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805793272864  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:32.661 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805793272865  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:32.745 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805793272865  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 11:10:32.745 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805793272864  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 11:10:32.751 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855805793272865  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:10:32.751 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855805793272864  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:10:32.751 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855805793272864  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:10:32.751 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855805793272865  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:10:32.768 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855805793272864  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 11:10:32.768 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855805793272865  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 11:10:32.893 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4855805793272865  [0;39m {dataSource-1} inited
2025-09-12 11:10:33.080 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855805793272865  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:10:33.080 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855805793272864  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:10:33.106 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855805793272864  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:10:33.106 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855805793272865  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:10:33.106 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855805793272864  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:10:33.106 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855805793272865  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:10:33.121 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805793272864  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:10:33.121 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805793272865  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:10:33.121 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805793272864  [0;39m ------------- 结束 耗时：465 ms -------------
2025-09-12 11:10:33.121 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805793272865  [0;39m ------------- 结束 耗时：465 ms -------------
2025-09-12 11:10:35.029 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805871031328  [0;39m ------------- 开始 -------------
2025-09-12 11:10:35.029 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805871031328  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 11:10:35.032 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805871031328  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 11:10:35.032 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805871031328  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:35.032 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805871031328  [0;39m 请求参数: []
2025-09-12 11:10:35.043 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855805871031328  [0;39m 获取维修店1预约统计成功
2025-09-12 11:10:35.045 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805871555616  [0;39m ------------- 开始 -------------
2025-09-12 11:10:35.045 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805871555616  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 11:10:35.045 INFO  com.gec.wiki.controller.OrderController           :213  [32m4855805871031328  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 11:10:35.045 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805871555616  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 11:10:35.045 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805871555616  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:35.045 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805871555616  [0;39m 请求参数: [1,10,""]
2025-09-12 11:10:35.046 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805871031328  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 11:10:35.046 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805871031328  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 11:10:35.057 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4855805871555616  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 11:10:35.057 INFO  com.gec.wiki.controller.OrderController           :65   [32m4855805871555616  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 11:10:35.057 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805871555616  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 11:10:35.057 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805871555616  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-12 11:10:37.134 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805940007968  [0;39m ------------- 开始 -------------
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855805940204576  [0;39m ------------- 开始 -------------
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805940007968  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805940007968  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855805940204576  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805940007968  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:37.143 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805940007968  [0;39m 请求参数: []
2025-09-12 11:10:37.141 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855805940204576  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 11:10:37.143 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855805940204576  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:37.143 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855805940204576  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 11:10:37.143 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4855805940007968  [0;39m 📋 获取服务分类列表
2025-09-12 11:10:37.143 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4855805940204576  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 11:10:37.150 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4855805940007968  [0;39m 🌲 构建分类树形结构
2025-09-12 11:10:37.148 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4855805940204576  [0;39m 根据token未找到用户信息，token可能已过期: Bearer 43b84acf69e24fdf8119971420c9451d
2025-09-12 11:10:37.150 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805940204576  [0;39m 返回结果: {"message":"用户未登录或非维修店用户","success":false}
2025-09-12 11:10:37.150 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805940204576  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 11:10:37.175 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4855805940007968  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 11:10:37.183 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855805940007968  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 11:10:37.183 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855805940007968  [0;39m ------------- 结束 耗时：49 ms -------------
2025-09-12 11:10:43.562 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806150640672  [0;39m ------------- 开始 -------------
2025-09-12 11:10:43.562 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806150640673  [0;39m ------------- 开始 -------------
2025-09-12 11:10:43.564 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806150640672  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:10:43.564 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806150640673  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:10:43.565 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806150640673  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:10:43.564 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806150640672  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:10:43.565 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806150640673  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:43.565 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806150640672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:43.565 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806150640673  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 11:10:43.565 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806150640672  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 11:10:43.565 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855806150640673  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:10:43.565 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855806150640672  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:10:43.567 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855806150640672  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:10:43.567 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855806150640673  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:10:43.567 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855806150640673  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 11:10:43.567 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855806150640672  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 11:10:43.585 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855806150640673  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:10:43.585 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855806150640672  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:10:43.594 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855806150640672  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:10:43.594 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855806150640673  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:10:43.594 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855806150640672  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:10:43.594 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855806150640673  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:10:43.594 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806150640673  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:10:43.594 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806150640672  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:10:43.594 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806150640673  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-12 11:10:43.594 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806150640672  [0;39m ------------- 结束 耗时：32 ms -------------
2025-09-12 11:10:48.532 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806313497632  [0;39m ------------- 开始 -------------
2025-09-12 11:10:48.532 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806313497632  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-12 11:10:48.532 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806313497632  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-12 11:10:48.532 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806313497632  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:48.537 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806313497632  [0;39m 请求参数: [{"username":"小石头的店铺"}]
2025-09-12 11:10:48.539 INFO  com.gec.wiki.service.impl.UserServiceImpl         :69   [32m4855806313497632  [0;39m 🔐 用户登录尝试：username=小石头的店铺, ip=0:0:0:0:0:0:0:1
2025-09-12 11:10:48.544 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4855806313497632  [0;39m 🔒 检查用户锁定状态：username=小石头的店铺
2025-09-12 11:10:48.550 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :58   [32m4855806313497632  [0;39m ⏰ 用户锁定已过期，清除锁定时间但保留失败记录：username=小石头的店铺
2025-09-12 11:10:48.550 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :131  [32m4855806313497632  [0;39m 🧹 清除锁定时间：username=小石头的店铺
2025-09-12 11:10:48.571 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :142  [32m4855806313497632  [0;39m ✅ 成功清除锁定时间：username=小石头的店铺, 更新记录数=1
2025-09-12 11:10:48.713 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4855806313497632  [0;39m 🧹 清除登录失败记录：username=小石头的店铺
2025-09-12 11:10:48.720 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4855806313497632  [0;39m ✅ 成功清除登录失败记录：username=小石头的店铺, 清除记录数=1
2025-09-12 11:10:48.727 INFO  com.gec.wiki.utils.TokenManager                   :32   [32m4855806313497632  [0;39m 存储token成功: userId=6, username=小石头的店铺
2025-09-12 11:10:48.727 INFO  com.gec.wiki.service.impl.UserServiceImpl         :169  [32m4855806313497632  [0;39m ✅ 用户登录成功：username=小石头的店铺, userType=2, ip=0:0:0:0:0:0:0:1
2025-09-12 11:10:48.727 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806313497632  [0;39m 返回结果: {"content":{"userInfo":{"realName":"恶梦","phone":"***********","id":6,"userType":2,"email":"<EMAIL>","username":"小石头的店铺"},"token":"6676f97bf71e41f89d33fb040bbd383c"},"message":"登录成功","success":true}
2025-09-12 11:10:48.727 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806313497632  [0;39m ------------- 结束 耗时：195 ms -------------
2025-09-12 11:10:48.849 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806323852320  [0;39m ------------- 开始 -------------
2025-09-12 11:10:48.849 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806323885088  [0;39m ------------- 开始 -------------
2025-09-12 11:10:48.849 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806323885088  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 11:10:48.849 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806323852320  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 11:10:48.849 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806323885088  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 11:10:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806323885088  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806323852320  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 11:10:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806323852320  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806323885088  [0;39m 请求参数: []
2025-09-12 11:10:48.851 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806323852320  [0;39m 请求参数: []
2025-09-12 11:10:48.851 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855806323852320  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 11:10:48.851 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855806323885088  [0;39m 📊 获取维修店预约统计
2025-09-12 11:10:48.859 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855806323885088  [0;39m 获取维修店1预约统计成功
2025-09-12 11:10:48.859 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806323885088  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 11:10:48.859 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806323885088  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 11:10:48.865 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855806323852320  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 11:10:48.865 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806323852320  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 11:10:48.865 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806323852320  [0;39m ------------- 结束 耗时：17 ms -------------
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806378902560  [0;39m ------------- 开始 -------------
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806378902561  [0;39m ------------- 开始 -------------
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806378902560  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806378902561  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806378902560  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806378902561  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806378902560  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:50.528 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806378902561  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:50.530 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806378902560  [0;39m 请求参数: []
2025-09-12 11:10:50.530 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806378902561  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 11:10:50.530 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4855806378902560  [0;39m 📋 获取服务分类列表
2025-09-12 11:10:50.530 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4855806378902560  [0;39m 🌲 构建分类树形结构
2025-09-12 11:10:50.530 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4855806378902561  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 11:10:50.530 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :336  [32m4855806378902561  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}, shopId: 6
2025-09-12 11:10:50.530 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :382  [32m4855806378902561  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-12 11:10:50.538 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4855806378902560  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 11:10:50.538 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806378902560  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 11:10:50.538 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806378902560  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 11:10:50.553 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :386  [32m4855806378902561  [0;39m 📋 维修店 6 查询结果：共 1 条记录，当前页 1 条
2025-09-12 11:10:50.558 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :402  [32m4855806378902561  [0;39m ✅ 维修店 6 服务查询完成，返回 1 条记录
2025-09-12 11:10:50.558 INFO  com.gec.wiki.controller.ShopServiceController     :74   [32m4855806378902561  [0;39m ✅ 维修店 6 查询到 1 条服务记录
2025-09-12 11:10:50.558 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806378902561  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"}],"total":1},"message":"查询成功","success":true}
2025-09-12 11:10:50.561 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806378902561  [0;39m ------------- 结束 耗时：33 ms -------------
2025-09-12 11:10:52.464 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806442341408  [0;39m ------------- 开始 -------------
2025-09-12 11:10:52.464 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806442341408  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:10:52.464 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806442341408  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:10:52.464 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806442341408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:52.464 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806442341408  [0;39m 请求参数: [1,10,""]
2025-09-12 11:10:52.471 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806442341408  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:10:52.477 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806442341408  [0;39m ------------- 结束 耗时：13 ms -------------
2025-09-12 11:10:52.512 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806443914272  [0;39m ------------- 开始 -------------
2025-09-12 11:10:52.512 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806443914272  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:10:52.512 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806443914272  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:10:52.513 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806443914272  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:10:52.513 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806443914272  [0;39m 请求参数: []
2025-09-12 11:10:52.534 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806443914272  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:10:52.537 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806443914272  [0;39m ------------- 结束 耗时：25 ms -------------
2025-09-12 11:11:01.287 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806731453472  [0;39m ------------- 开始 -------------
2025-09-12 11:11:01.287 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806731453472  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:11:01.287 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806731453472  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:11:01.289 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806731453472  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:01.289 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806731453472  [0;39m 请求参数: [1,10,""]
2025-09-12 11:11:01.289 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806731453472  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:11:01.289 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806731453472  [0;39m ------------- 结束 耗时：3 ms -------------
2025-09-12 11:11:01.310 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855806732207136  [0;39m ------------- 开始 -------------
2025-09-12 11:11:01.310 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855806732207136  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:11:01.310 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855806732207136  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:11:01.315 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855806732207136  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:01.315 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855806732207136  [0;39m 请求参数: []
2025-09-12 11:11:01.315 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855806732207136  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:11:01.317 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855806732207136  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855807046616097  [0;39m ------------- 开始 -------------
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855807046616096  [0;39m ------------- 开始 -------------
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855807046616097  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855807046616096  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855807046616097  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855807046616096  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855807046616096  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855807046616097  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855807046616096  [0;39m 请求参数: []
2025-09-12 11:11:10.905 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855807046616097  [0;39m 请求参数: []
2025-09-12 11:11:10.905 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855807046616096  [0;39m 📊 获取维修店预约统计
2025-09-12 11:11:10.905 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855807046616097  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 11:11:10.910 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855807046616097  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 11:11:10.910 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855807046616096  [0;39m 获取维修店1预约统计成功
2025-09-12 11:11:10.910 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855807046616097  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 11:11:10.910 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855807046616096  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 11:11:10.910 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855807046616096  [0;39m ------------- 结束 耗时：5 ms -------------
2025-09-12 11:11:10.910 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855807046616097  [0;39m ------------- 结束 耗时：5 ms -------------
2025-09-12 11:11:18.093 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855807282152480  [0;39m ------------- 开始 -------------
2025-09-12 11:11:18.099 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855807282152480  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:11:18.099 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855807282152480  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:11:18.099 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855807282152480  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:18.100 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855807282152480  [0;39m 请求参数: [1,10,""]
2025-09-12 11:11:18.100 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855807282152480  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:11:18.100 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855807282152480  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855807283135520  [0;39m ------------- 开始 -------------
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855807283135520  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855807283135520  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855807283135520  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855807283135520  [0;39m 请求参数: []
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855807283135520  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:11:18.123 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855807283135520  [0;39m ------------- 结束 耗时：0 ms -------------
2025-09-12 11:13:44.447 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855812077880352  [0;39m ------------- 开始 -------------
2025-09-12 11:13:44.447 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855812077880352  [0;39m 请求地址: http://localhost:8880/api/shop/technicians POST
2025-09-12 11:13:44.451 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855812077880352  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.addTechnician
2025-09-12 11:13:44.451 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855812077880352  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:13:44.455 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855812077880352  [0;39m 请求参数: [{"name":"罗技师","phone":"19977452689"}]
2025-09-12 11:13:44.455 INFO  com.gec.wiki.controller.TechnicianController      :57   [32m4855812077880352  [0;39m 添加技师: 罗技师
2025-09-12 11:13:44.458 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855812077880352  [0;39m 返回结果: {"message":"技师添加成功","success":true}
2025-09-12 11:13:44.458 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855812077880352  [0;39m ------------- 结束 耗时：11 ms -------------
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855812079813664  [0;39m ------------- 开始 -------------
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855812079813664  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855812079813664  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855812079813664  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855812079813664  [0;39m 请求参数: [1,10,""]
2025-09-12 11:13:44.506 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855812079813664  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:13:44.513 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855812079813664  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 11:13:44.537 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855812080829472  [0;39m ------------- 开始 -------------
2025-09-12 11:13:44.541 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855812080829472  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:13:44.542 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855812080829472  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:13:44.542 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855812080829472  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:13:44.542 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855812080829472  [0;39m 请求参数: []
2025-09-12 11:13:44.542 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855812080829472  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:13:44.542 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855812080829472  [0;39m ------------- 结束 耗时：5 ms -------------
2025-09-12 11:25:27.073 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855835101529120  [0;39m ------------- 开始 -------------
2025-09-12 11:25:27.073 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855835101529120  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:25:27.076 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855835101529120  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:25:27.076 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855835101529120  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:25:27.076 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855835101529120  [0;39m 请求参数: [1,10,""]
2025-09-12 11:25:27.078 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855835101529120  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:25:27.078 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855835101529120  [0;39m ------------- 结束 耗时：5 ms -------------
2025-09-12 11:25:27.147 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855835103953952  [0;39m ------------- 开始 -------------
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855835103953952  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855835103953952  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855835103953952  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855835103953952  [0;39m 请求参数: []
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855835103953952  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:25:27.150 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855835103953952  [0;39m ------------- 结束 耗时：3 ms -------------
2025-09-12 11:26:17.396 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855836750513184  [0;39m ------------- 开始 -------------
2025-09-12 11:26:17.396 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855836750513184  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:26:17.396 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855836750513184  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:26:17.398 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855836750513184  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:26:17.398 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855836750513184  [0;39m 请求参数: [1,10,""]
2025-09-12 11:26:17.398 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855836750513184  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:26:17.398 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855836750513184  [0;39m ------------- 结束 耗时：2 ms -------------
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855836751037472  [0;39m ------------- 开始 -------------
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855836751037472  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855836751037472  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855836751037472  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855836751037472  [0;39m 请求参数: []
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855836751037472  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:26:17.412 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855836751037472  [0;39m ------------- 结束 耗时：0 ms -------------
2025-09-12 11:30:08.252 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-12 11:30:08.259 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-12 11:30:15.441 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 25692 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 11:30:15.444 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 11:30:16.399 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 11:30:16.402 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 11:30:16.443 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-09-12 11:30:17.281 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 11:30:17.291 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 11:30:17.291 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 11:30:17.292 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 11:30:17.396 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 11:30:17.396 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1892 ms
2025-09-12 11:30:20.201 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 11:30:20.715 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 11:30:20.741 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 11:30:20.753 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.83 seconds (JVM running for 7.322)
2025-09-12 11:30:20.756 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 11:30:20.757 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 11:30:38.928 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 11:30:38.928 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 11:30:38.928 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-12 11:30:39.090 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845325603872  [0;39m ------------- 开始 -------------
2025-09-12 11:30:39.090 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845325603873  [0;39m ------------- 开始 -------------
2025-09-12 11:30:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845325603873  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:30:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845325603872  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 11:30:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845325603873  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:30:39.091 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845325603872  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 11:30:39.094 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845325603873  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:39.094 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845325603872  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:39.183 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845325603873  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 11:30:39.183 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845325603872  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 11:30:39.189 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855845325603873  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:30:39.189 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4855845325603872  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:30:39.197 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855845325603873  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 11:30:39.197 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4855845325603872  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 11:30:39.211 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855845325603872  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 11:30:39.213 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4855845325603873  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 11:30:39.342 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4855845325603873  [0;39m {dataSource-1} inited
2025-09-12 11:30:39.564 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855845325603872  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:30:39.564 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4855845325603873  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 11:30:39.592 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855845325603872  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:30:39.592 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4855845325603873  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 11:30:39.592 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855845325603872  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:30:39.592 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4855845325603873  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 11:30:39.608 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845325603872  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:30:39.608 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845325603873  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 11:30:39.608 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845325603872  [0;39m ------------- 结束 耗时：521 ms -------------
2025-09-12 11:30:39.608 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845325603873  [0;39m ------------- 结束 耗时：521 ms -------------
2025-09-12 11:30:45.969 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845551113248  [0;39m ------------- 开始 -------------
2025-09-12 11:30:45.969 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845551113248  [0;39m 请求地址: http://localhost:8880/auth/login POST
2025-09-12 11:30:45.969 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845551113248  [0;39m 类名方法: com.gec.wiki.controller.AuthController.login
2025-09-12 11:30:45.976 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845551113248  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:45.976 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845551113248  [0;39m 请求参数: [{"username":"小石头的店铺"}]
2025-09-12 11:30:45.985 INFO  com.gec.wiki.service.impl.UserServiceImpl         :69   [32m4855845551113248  [0;39m 🔐 用户登录尝试：username=小石头的店铺, ip=0:0:0:0:0:0:0:1
2025-09-12 11:30:45.985 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :42   [32m4855845551113248  [0;39m 🔒 检查用户锁定状态：username=小石头的店铺
2025-09-12 11:30:45.997 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :58   [32m4855845551113248  [0;39m ⏰ 用户锁定已过期，清除锁定时间但保留失败记录：username=小石头的店铺
2025-09-12 11:30:45.998 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :131  [32m4855845551113248  [0;39m 🧹 清除锁定时间：username=小石头的店铺
2025-09-12 11:30:46.027 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :142  [32m4855845551113248  [0;39m ✅ 成功清除锁定时间：username=小石头的店铺, 更新记录数=1
2025-09-12 11:30:46.179 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :155  [32m4855845551113248  [0;39m 🧹 清除登录失败记录：username=小石头的店铺
2025-09-12 11:30:46.187 INFO  com.gec.wiki.service.impl.LoginLockServiceImpl    :168  [32m4855845551113248  [0;39m ✅ 成功清除登录失败记录：username=小石头的店铺, 清除记录数=1
2025-09-12 11:30:46.196 INFO  com.gec.wiki.utils.TokenManager                   :32   [32m4855845551113248  [0;39m 存储token成功: userId=6, username=小石头的店铺
2025-09-12 11:30:46.199 INFO  com.gec.wiki.service.impl.UserServiceImpl         :169  [32m4855845551113248  [0;39m ✅ 用户登录成功：username=小石头的店铺, userType=2, ip=0:0:0:0:0:0:0:1
2025-09-12 11:30:46.201 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845551113248  [0;39m 返回结果: {"content":{"userInfo":{"realName":"恶梦","phone":"***********","id":6,"userType":2,"email":"<EMAIL>","username":"小石头的店铺"},"token":"f23fc58e411741ae9a0301f4fab42bce"},"message":"登录成功","success":true}
2025-09-12 11:30:46.201 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845551113248  [0;39m ------------- 结束 耗时：232 ms -------------
2025-09-12 11:30:46.335 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845563106336  [0;39m ------------- 开始 -------------
2025-09-12 11:30:46.335 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845563106337  [0;39m ------------- 开始 -------------
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845563106336  [0;39m 请求地址: http://localhost:8880/shop/booking/stats GET
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845563106337  [0;39m 请求地址: http://localhost:8880/shop/booking/today GET
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845563106337  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getTodayBookings
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845563106336  [0;39m 类名方法: com.gec.wiki.controller.ShopBookingController.getBookingStats
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845563106336  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845563106337  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845563106337  [0;39m 请求参数: []
2025-09-12 11:30:46.342 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845563106336  [0;39m 请求参数: []
2025-09-12 11:30:46.349 INFO  com.gec.wiki.controller.ShopBookingController     :30   [32m4855845563106337  [0;39m 🏪 获取维修店今日预约列表
2025-09-12 11:30:46.349 INFO  com.gec.wiki.controller.ShopBookingController     :91   [32m4855845563106336  [0;39m 📊 获取维修店预约统计
2025-09-12 11:30:46.358 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :287  [32m4855845563106337  [0;39m 获取维修店1今日预约列表成功，共0条
2025-09-12 11:30:46.358 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4855845563106336  [0;39m 获取维修店1预约统计成功
2025-09-12 11:30:46.358 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845563106336  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"success":true}
2025-09-12 11:30:46.358 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845563106337  [0;39m 返回结果: {"content":[],"success":true}
2025-09-12 11:30:46.358 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845563106337  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-12 11:30:46.358 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845563106336  [0;39m ------------- 结束 耗时：23 ms -------------
2025-09-12 11:30:51.941 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845746803744  [0;39m ------------- 开始 -------------
2025-09-12 11:30:51.941 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845746803744  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 11:30:51.946 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845746803744  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 11:30:51.947 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845746803744  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:51.947 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845746803744  [0;39m 请求参数: [1,10,""]
2025-09-12 11:30:51.949 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845746803744  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 11:30:51.949 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845746803744  [0;39m ------------- 结束 耗时：8 ms -------------
2025-09-12 11:30:51.969 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4855845747721248  [0;39m ------------- 开始 -------------
2025-09-12 11:30:51.969 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4855845747721248  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 11:30:51.969 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4855845747721248  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 11:30:51.969 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4855845747721248  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 11:30:51.969 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4855845747721248  [0;39m 请求参数: []
2025-09-12 11:30:51.984 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4855845747721248  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 11:30:51.984 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4855845747721248  [0;39m ------------- 结束 耗时：15 ms -------------
2025-09-12 11:32:46.319 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-12 11:32:46.326 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-12 15:06:15.530 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 11104 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 15:06:15.534 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 15:06:16.604 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 15:06:16.608 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 15:06:16.650 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-09-12 15:06:17.516 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 15:06:17.529 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 15:06:17.530 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 15:06:17.531 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 15:06:17.682 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 15:06:17.683 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 2084 ms
2025-09-12 15:06:20.090 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 15:06:20.514 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 15:06:20.543 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 15:06:20.553 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.673 seconds (JVM running for 7.048)
2025-09-12 15:06:20.555 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 15:06:20.556 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 15:06:39.360 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 15:06:39.360 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 15:06:39.364 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 4 ms
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270011630625  [0;39m ------------- 开始 -------------
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270011630624  [0;39m ------------- 开始 -------------
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270011630624  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270011630625  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270011630624  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 15:06:39.476 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270011630625  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 15:06:39.482 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270011630624  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:39.482 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270011630625  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:39.567 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270011630624  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 15:06:39.567 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270011630625  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 15:06:39.574 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4856270011630625  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 15:06:39.574 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4856270011630624  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 15:06:39.583 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4856270011630625  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 15:06:39.583 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4856270011630624  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 15:06:39.591 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4856270011630624  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 15:06:39.591 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4856270011630625  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 15:06:39.754 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4856270011630625  [0;39m {dataSource-1} inited
2025-09-12 15:06:39.973 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4856270011630624  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 15:06:39.973 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4856270011630625  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 15:06:39.999 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4856270011630625  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 15:06:39.999 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4856270011630624  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 15:06:39.999 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4856270011630625  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 15:06:39.999 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4856270011630624  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 15:06:40.012 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270011630625  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 15:06:40.012 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270011630624  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 15:06:40.012 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270011630624  [0;39m ------------- 结束 耗时：536 ms -------------
2025-09-12 15:06:40.012 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270011630625  [0;39m ------------- 结束 耗时：536 ms -------------
2025-09-12 15:06:42.978 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270126384160  [0;39m ------------- 开始 -------------
2025-09-12 15:06:42.978 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270126384160  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 15:06:42.978 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270126384160  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 15:06:42.978 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270126384160  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:42.983 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270126384160  [0;39m 请求参数: []
2025-09-12 15:06:42.991 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270126810144  [0;39m ------------- 开始 -------------
2025-09-12 15:06:42.991 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270126810144  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 15:06:42.995 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270126810144  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 15:06:42.995 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270126810144  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:42.995 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270126810144  [0;39m 请求参数: [1,10,""]
2025-09-12 15:06:42.999 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4856270126384160  [0;39m 获取维修店1预约统计成功
2025-09-12 15:06:42.999 INFO  com.gec.wiki.controller.OrderController           :213  [32m4856270126384160  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 15:06:42.999 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270126384160  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 15:06:42.999 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270126384160  [0;39m ------------- 结束 耗时：21 ms -------------
2025-09-12 15:06:43.015 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4856270126810144  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 15:06:43.015 INFO  com.gec.wiki.controller.OrderController           :65   [32m4856270126810144  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 15:06:43.015 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270126810144  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 15:06:43.018 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270126810144  [0;39m ------------- 结束 耗时：27 ms -------------
2025-09-12 15:06:44.588 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270179140640  [0;39m ------------- 开始 -------------
2025-09-12 15:06:44.589 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270179140640  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 15:06:44.589 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270179173408  [0;39m ------------- 开始 -------------
2025-09-12 15:06:44.589 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270179173408  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270179173408  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270179140640  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270179173408  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270179140640  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270179173408  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 15:06:44.591 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270179140640  [0;39m 请求参数: []
2025-09-12 15:06:44.598 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4856270179140640  [0;39m 📋 获取服务分类列表
2025-09-12 15:06:44.596 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4856270179173408  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 15:06:44.598 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856270179173408  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:06:44.599 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270179173408  [0;39m 返回结果: {"message":"用户未登录或非维修店用户","success":false}
2025-09-12 15:06:44.599 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270179173408  [0;39m ------------- 结束 耗时：10 ms -------------
2025-09-12 15:06:44.602 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4856270179140640  [0;39m 🌲 构建分类树形结构
2025-09-12 15:06:44.624 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4856270179140640  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 15:06:44.629 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270179140640  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 15:06:44.630 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270179140640  [0;39m ------------- 结束 耗时：42 ms -------------
2025-09-12 15:06:52.562 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270440432672  [0;39m ------------- 开始 -------------
2025-09-12 15:06:52.562 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270440432672  [0;39m 请求地址: http://localhost:8880/api/shop/technicians GET
2025-09-12 15:06:52.562 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270440432672  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicians
2025-09-12 15:06:52.562 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270440432672  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:52.562 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270440432672  [0;39m 请求参数: [1,10,""]
2025-09-12 15:06:52.570 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270440432672  [0;39m 返回结果: {"content":{"list":[],"total":0},"message":"获取技师列表成功","success":true}
2025-09-12 15:06:52.574 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270440432672  [0;39m ------------- 结束 耗时：12 ms -------------
2025-09-12 15:06:52.609 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856270441972768  [0;39m ------------- 开始 -------------
2025-09-12 15:06:52.611 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856270441972768  [0;39m 请求地址: http://localhost:8880/api/shop/technicians/stats GET
2025-09-12 15:06:52.611 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856270441972768  [0;39m 类名方法: com.gec.wiki.controller.TechnicianController.getTechnicianStats
2025-09-12 15:06:52.611 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856270441972768  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:06:52.611 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856270441972768  [0;39m 请求参数: []
2025-09-12 15:06:52.633 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856270441972768  [0;39m 返回结果: {"content":{"total":12,"todayServices":25,"online":8,"avgRating":4.5},"message":"获取统计数据成功","success":true}
2025-09-12 15:06:52.635 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856270441972768  [0;39m ------------- 结束 耗时：26 ms -------------
2025-09-12 15:20:50.153 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 13508 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 15:20:50.155 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 15:20:50.879 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 15:20:50.883 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 15:20:50.919 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-09-12 15:20:51.508 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 15:20:51.516 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 15:20:51.517 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 15:20:51.517 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 15:20:51.616 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 15:20:51.617 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1418 ms
2025-09-12 15:20:53.776 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 15:20:54.155 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 15:20:54.174 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 15:20:54.182 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 4.418 seconds (JVM running for 6.217)
2025-09-12 15:20:54.184 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 15:20:54.185 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-12 15:21:12.907 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-12 15:21:12.908 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-12 15:21:12.909 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-12 15:21:13.026 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298635887649  [0;39m ------------- 开始 -------------
2025-09-12 15:21:13.026 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298635887648  [0;39m ------------- 开始 -------------
2025-09-12 15:21:13.026 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298635887649  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 15:21:13.026 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298635887648  [0;39m 请求地址: http://localhost:8880/service/getServiceListByPage GET
2025-09-12 15:21:13.027 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298635887649  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 15:21:13.027 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298635887648  [0;39m 类名方法: com.gec.wiki.controller.ServiceController.getServiceListByPage
2025-09-12 15:21:13.027 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298635887648  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:13.027 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298635887649  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:13.096 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298635887649  [0;39m 请求参数: [{"page":1,"size":1000}]
2025-09-12 15:21:13.096 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298635887648  [0;39m 请求参数: [{"page":1,"size":6}]
2025-09-12 15:21:13.103 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4856298635887649  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 15:21:13.103 INFO  com.gec.wiki.controller.ServiceController         :39   [32m4856298635887648  [0;39m 🔍 分页查询服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 15:21:13.110 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4856298635887649  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=1000}
2025-09-12 15:21:13.110 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :41   [32m4856298635887648  [0;39m 🔍 构建服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=6}
2025-09-12 15:21:13.118 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4856298635887648  [0;39m 🔢 执行分页查询：页码=1, 页大小=6
2025-09-12 15:21:13.118 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :84   [32m4856298635887649  [0;39m 🔢 执行分页查询：页码=1, 页大小=1000
2025-09-12 15:21:13.249 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4856298635887648  [0;39m {dataSource-1} inited
2025-09-12 15:21:13.424 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4856298635887648  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 15:21:13.424 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :88   [32m4856298635887649  [0;39m 📋 数据库查询结果：共 3 条记录，当前页 3 条
2025-09-12 15:21:13.448 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4856298635887648  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 15:21:13.449 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4856298635887648  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 15:21:13.449 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :104  [32m4856298635887649  [0;39m ✅ 服务查询完成，返回 3 条记录
2025-09-12 15:21:13.449 INFO  com.gec.wiki.controller.ServiceController         :53   [32m4856298635887649  [0;39m 📊 查询结果：共 3 条记录
2025-09-12 15:21:13.456 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298635887649  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 15:21:13.456 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298635887648  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"nice","cover":"/image/a69fdab7-233b-486c-aa72-062ed83c0ff8_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-11T21:41:09","description":"窗户很好","duration":60,"id":4834096586359849,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":6,"status":1,"updateTime":"2025-09-11T21:41:09"},{"bookingCount":0,"category1Id":4848309795718176,"category2Id":4848310685074464,"categoryName":"窗户换新","completeCount":0,"content":"高品质","cover":"/image/f9a474cf-19b7-48b9-b45b-33d16f51e965_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T22:40:19","description":"窗户换新很好","duration":60,"id":4834096586359848,"isRecommend":1,"name":"车窗维修","originalPrice":60.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:40:19"},{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/c9cecce1-32d5-4d74-be87-82523d8d580a_f0717382681cec10a30b5ed12007498b.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修pro","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T22:03:52"}],"total":3},"message":"查询成功","success":true}
2025-09-12 15:21:13.456 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298635887649  [0;39m ------------- 结束 耗时：437 ms -------------
2025-09-12 15:21:13.456 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298635887648  [0;39m ------------- 结束 耗时：437 ms -------------
2025-09-12 15:21:19.424 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298845766688  [0;39m ------------- 开始 -------------
2025-09-12 15:21:19.424 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298845766688  [0;39m 请求地址: http://localhost:8880/api/shop/orders/stats GET
2025-09-12 15:21:19.424 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298845766688  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getOrderStats
2025-09-12 15:21:19.424 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298845766688  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:19.428 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298845766688  [0;39m 请求参数: []
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298846290976  [0;39m ------------- 开始 -------------
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298846290976  [0;39m 请求地址: http://localhost:8880/api/shop/orders GET
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298846290976  [0;39m 类名方法: com.gec.wiki.controller.OrderController.getShopOrders
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298846290976  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298846290976  [0;39m 请求参数: [1,10,""]
2025-09-12 15:21:19.440 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :352  [32m4856298845766688  [0;39m 获取维修店1预约统计成功
2025-09-12 15:21:19.440 INFO  com.gec.wiki.controller.OrderController           :213  [32m4856298845766688  [0;39m 返回真实数据库统计数据: {monthlyRevenue=120.00, completedOrders=2, pendingOrders=0, processingOrders=1}
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298845766688  [0;39m 返回结果: {"content":{"monthlyRevenue":120.00,"completedOrders":2,"pendingOrders":0,"processingOrders":1},"message":"获取统计数据成功","success":true}
2025-09-12 15:21:19.440 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298845766688  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 15:21:19.464 INFO  com.gec.wiki.service.impl.BookingServiceImpl      :405  [32m4856298846290976  [0;39m 获取维修店1预约列表成功，共4条
2025-09-12 15:21:19.464 INFO  com.gec.wiki.controller.OrderController           :65   [32m4856298846290976  [0;39m 返回真实数据库订单数据，总数: 4, 页面: 1, 大小: 10
2025-09-12 15:21:19.464 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298846290976  [0;39m 返回结果: {"content":{"list":[{"customerPhone":"19978452934","amount":60.00,"requirements":"12","orderNumber":"B202509104850727976502304","appointmentTime":"2025-09-10 10:00:00","vehicleInfo":"未知车辆","id":4,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":5},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850416995533856","appointmentTime":"2025-09-10 15:00:00","vehicleInfo":"未知车辆","id":3,"serviceName":"车窗维修","customerName":"韦茹萍","status":3},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850399118459936","appointmentTime":"2025-09-11 09:00:00","vehicleInfo":"未知车辆","id":2,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4},{"customerPhone":"19978452934","amount":60.00,"requirements":"111","orderNumber":"B202509104850394094076960","appointmentTime":"2025-09-10 12:00:00","vehicleInfo":"未知车辆","id":1,"serviceName":"汽车维修pro","customerName":"韦茹萍","status":4}],"total":4},"message":"获取订单列表成功","success":true}
2025-09-12 15:21:19.464 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298846290976  [0;39m ------------- 结束 耗时：24 ms -------------
2025-09-12 15:21:20.741 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298888922144  [0;39m ------------- 开始 -------------
2025-09-12 15:21:20.748 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298888922144  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 15:21:20.748 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298888922144  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 15:21:20.748 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298888922144  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:20.748 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856298889151520  [0;39m ------------- 开始 -------------
2025-09-12 15:21:20.750 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856298889151520  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 15:21:20.750 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856298889151520  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 15:21:20.748 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298888922144  [0;39m 请求参数: []
2025-09-12 15:21:20.752 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856298889151520  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:21:20.754 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856298889151520  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 15:21:20.758 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4856298889151520  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 15:21:20.758 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4856298888922144  [0;39m 📋 获取服务分类列表
2025-09-12 15:21:20.762 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856298889151520  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:21:20.764 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298889151520  [0;39m 返回结果: {"message":"用户未登录或非维修店用户","success":false}
2025-09-12 15:21:20.764 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298889151520  [0;39m ------------- 结束 耗时：16 ms -------------
2025-09-12 15:21:20.764 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4856298888922144  [0;39m 🌲 构建分类树形结构
2025-09-12 15:21:20.793 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4856298888922144  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 15:21:20.796 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856298888922144  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 15:21:20.796 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856298888922144  [0;39m ------------- 结束 耗时：55 ms -------------
2025-09-12 15:23:37.483 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856303369684000  [0;39m ------------- 开始 -------------
2025-09-12 15:23:37.485 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856303369684000  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-12 15:23:37.487 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856303369684000  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-12 15:23:37.488 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856303369684000  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:23:37.488 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4856303369847840  [0;39m ------------- 开始 -------------
2025-09-12 15:23:37.490 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856303369684000  [0;39m 请求参数: []
2025-09-12 15:23:37.490 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4856303369847840  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-12 15:23:37.490 INFO  com.gec.wiki.controller.ShopServiceController     :90   [32m4856303369684000  [0;39m 📋 获取服务分类列表
2025-09-12 15:23:37.490 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4856303369684000  [0;39m 🌲 构建分类树形结构
2025-09-12 15:23:37.490 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4856303369847840  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-12 15:23:37.490 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4856303369847840  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-12 15:23:37.490 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4856303369847840  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-12 15:23:37.492 INFO  com.gec.wiki.controller.ShopServiceController     :52   [32m4856303369847840  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, shopId=null, page=1, size=10}
2025-09-12 15:23:37.492 ERROR com.gec.wiki.controller.ShopServiceController     :360  [32m4856303369847840  [0;39m 根据token未找到用户信息，token可能已过期: Bearer f23fc58e411741ae9a0301f4fab42bce
2025-09-12 15:23:37.493 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856303369847840  [0;39m 返回结果: {"message":"用户未登录或非维修店用户","success":false}
2025-09-12 15:23:37.495 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856303369847840  [0;39m ------------- 结束 耗时：7 ms -------------
2025-09-12 15:23:37.531 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4856303369684000  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 136732
2025-09-12 15:23:37.534 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4856303369684000  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 144086
2025-09-12 15:23:37.557 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4856303369684000  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-12 15:23:37.558 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4856303369684000  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-12 15:23:37.558 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4856303369684000  [0;39m ------------- 结束 耗时：75 ms -------------
2025-09-12 15:30:13.562 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-1} closing ...
2025-09-12 15:30:13.570 INFO  com.alibaba.druid.pool.DruidDataSource            :2116 [32m                  [0;39m {dataSource-1} closed
2025-09-12 15:39:22.839 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 28216 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 15:39:22.841 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 15:39:23.624 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 15:39:23.627 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 15:39:23.665 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-09-12 15:39:24.383 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 15:39:24.395 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 15:39:24.395 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 15:39:24.396 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 15:39:24.496 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 15:39:24.498 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1596 ms
2025-09-12 15:39:26.803 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 15:39:27.240 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 15:39:27.246 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8880 is already in use
2025-09-12 15:39:27.269 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-12 15:39:27.271 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8880"]
2025-09-12 15:39:27.271 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-12 15:39:27.281 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8880"]
2025-09-12 15:39:27.282 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8880"]
2025-09-12 15:39:27.291 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-12 15:39:27.314 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-09-12 15:40:32.681 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 1.8.0_442 on LAPTOP-4VB8OLQM with PID 25460 (D:\JavaCar\wiki\wiki\target\classes started by fls in D:\JavaCar\wiki)
2025-09-12 15:40:32.686 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-12 15:40:33.582 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-12 15:40:33.585 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-12 15:40:33.629 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-09-12 15:40:34.477 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-12 15:40:34.488 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-12 15:40:34.488 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-12 15:40:34.489 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-12 15:40:34.614 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-12 15:40:34.614 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 1867 ms
2025-09-12 15:40:36.695 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-12 15:40:37.225 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-12 15:40:37.243 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-12 15:40:37.251 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 5.14 seconds (JVM running for 6.831)
2025-09-12 15:40:37.253 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-12 15:40:37.253 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
