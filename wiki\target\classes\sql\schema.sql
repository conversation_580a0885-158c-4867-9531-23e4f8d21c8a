-- 系统设置表 - 用于存储管理员配置的登录错误锁定次数等系统参数
CREATE TABLE IF NOT EXISTS `system_settings` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `setting_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '设置键',
    `setting_value` VARCHAR(500) NOT NULL COMMENT '设置值',
    `setting_description` VARCHAR(200) COMMENT '设置描述',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 用户登录锁定记录表
CREATE TABLE IF NOT EXISTS `user_login_lock` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `fail_count` INT DEFAULT 0 COMMENT '失败次数',
    `locked_until` DATETIME COMMENT '锁定到期时间',
    `last_fail_time` DATETIME COMMENT '最后失败时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    KEY `idx_username` (`username`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ip_address` (`ip_address`),
    KEY `idx_locked_until` (`locked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录锁定记录表';

-- 插入默认的登录错误锁定次数设置
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_description`)
VALUES ('LOGIN_MAX_FAIL_COUNT', '5', '登录最大错误次数，超过后锁定30分钟')
ON DUPLICATE KEY UPDATE
`setting_value` = VALUES(`setting_value`),
`setting_description` = VALUES(`setting_description`);

-- 创建维修店表
CREATE TABLE IF NOT EXISTS `shop` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(100) NOT NULL COMMENT '维修店名称',
  `address` VARCHAR(255) DEFAULT NULL COMMENT '维修店地址',
  `phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
  `business_hours` VARCHAR(50) DEFAULT NULL COMMENT '营业时间',
  `rating` DECIMAL(3,2) DEFAULT 4.0 COMMENT '评分',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态：1-营业中，0-已关闭',
  `description` TEXT DEFAULT NULL COMMENT '维修店描述',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_name` (`name`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修店表';

-- 插入维修店测试数据
INSERT INTO `shop` (`name`, `address`, `phone`, `business_hours`, `rating`, `status`, `description`) VALUES
('精工汽修店', '北京市朝阳区建国路88号', '010-12345678', '08:00-18:00', 4.5, 1, '专业的汽车维修服务，技术精湛，服务周到'),
('快速维修中心', '北京市海淀区中关村大街100号', '010-87654321', '09:00-19:00', 4.2, 1, '快速高效的汽车维修，节省您的宝贵时间'),
('专业汽车服务', '北京市丰台区丰台路50号', '010-11122334', '08:30-17:30', 4.8, 1, '一站式汽车服务，从保养到维修，应有尽有'),
('汽车维护专家', '北京市西城区复兴门大街25号', '010-55667788', '07:00-20:00', 4.6, 1, '资深技师团队，专业解决各种汽车问题'),
('一站式汽修', '北京市东城区王府井大街120号', '010-99887766', '08:00-19:00', 4.3, 1, '综合性汽车服务中心，满足您的各种需求'),
('精益求精维修店', '北京市石景山区石景山路88号', '010-44556677', '09:00-18:00', 4.7, 1, '精益求精的维修理念，为您的爱车保驾护航'),
('汽车之家维修中心', '北京市通州区通州大街200号', '010-33445566', '08:00-18:30', 4.4, 1, '温馨如家的服务环境，专业的维修技术'),
('快修连锁店', '北京市昌平区昌平路66号', '010-22334455', '08:30-19:30', 4.1, 1, '连锁经营，标准化服务，值得信赖的选择')
ON DUPLICATE KEY UPDATE
`name` = VALUES(`name`),
`address` = VALUES(`address`),
`phone` = VALUES(`phone`),
`business_hours` = VALUES(`business_hours`),
`rating` = VALUES(`rating`),
`status` = VALUES(`status`),
`description` = VALUES(`description`);

-- 创建技师信息表
CREATE TABLE IF NOT EXISTS `technicians` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '技师ID',
  `name` varchar(50) NOT NULL COMMENT '技师姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `experience` tinyint(4) NOT NULL COMMENT '工作经验：1=1年以下,2=1-3年,3=3-5年,4=5-10年,5=10年以上',
  `join_date` date DEFAULT NULL COMMENT '入职日期',
  `skills` json DEFAULT NULL COMMENT '专业技能(JSON格式)',
  `description` text COMMENT '技师简介',
  `shop_user_id` bigint(20) NOT NULL COMMENT '所属维修店用户ID',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0=离线,1=在线',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分(0-5分)',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `total_services` int(11) DEFAULT '0' COMMENT '总服务次数',
  `monthly_income` decimal(10,2) DEFAULT '0.00' COMMENT '本月收入',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_user_id` (`shop_user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_experience` (`experience`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师信息表';

-- 插入技师测试数据
INSERT INTO `technicians` (`name`, `phone`, `experience`, `join_date`, `skills`, `description`, `shop_user_id`, `status`, `rating`, `total_services`, `monthly_income`) VALUES
('张师傅', '13800138001', 3, '2023-01-15', '["发动机维修", "变速箱维修"]', '资深技师，专业维修经验丰富', 4, 1, 4.8, 145, 8500.00),
('李师傅', '13800138002', 4, '2022-06-20', '["刹车系统", "空调系统", "电子系统"]', '精通各类汽车电子系统维修', 4, 1, 4.6, 120, 7800.00),
('王师傅', '13800138003', 2, '2024-03-10', '["轮胎服务", "美容养护"]', '年轻有为，服务态度优秀', 4, 0, 4.3, 65, 4200.00),
('赵师傅', '13800138004', 3, '2023-06-01', '["发动机维修", "刹车系统"]', '经验丰富的维修专家', 6, 1, 4.7, 98, 6800.00),
('刘师傅', '13800138005', 2, '2024-01-20', '["电子系统", "空调系统"]', '年轻技师，技术过硬', 6, 1, 4.4, 56, 4500.00)
ON DUPLICATE KEY UPDATE
`name` = VALUES(`name`),
`phone` = VALUES(`phone`),
`experience` = VALUES(`experience`),
`join_date` = VALUES(`join_date`),
`skills` = VALUES(`skills`),
`description` = VALUES(`description`),
`rating` = VALUES(`rating`),
`total_services` = VALUES(`total_services`),
`monthly_income` = VALUES(`monthly_income`);
