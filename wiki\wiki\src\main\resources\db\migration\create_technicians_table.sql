-- 技师管理系统数据库初始化脚本
-- 执行此脚本前请确保已连接到正确的数据库

-- 创建技师信息表
CREATE TABLE IF NOT EXISTS `technicians` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '技师ID',
  `name` varchar(50) NOT NULL COMMENT '技师姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `experience` tinyint(4) NOT NULL COMMENT '工作经验：1=1年以下,2=1-3年,3=3-5年,4=5-10年,5=10年以上',
  `join_date` date DEFAULT NULL COMMENT '入职日期',
  `skills` json DEFAULT NULL COMMENT '专业技能(JSON格式)',
  `description` text COMMENT '技师简介',
  `shop_user_id` bigint(20) NOT NULL COMMENT '所属维修店用户ID',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0=离线,1=在线',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分(0-5分)',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `total_services` int(11) DEFAULT '0' COMMENT '总服务次数',
  `monthly_income` decimal(10,2) DEFAULT '0.00' COMMENT '本月收入',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_user_id` (`shop_user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_experience` (`experience`),
  CONSTRAINT `fk_technicians_shop_user` FOREIGN KEY (`shop_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师信息表';

-- 创建技师排班表（可选，用于未来扩展）
CREATE TABLE IF NOT EXISTS `technician_schedules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `technician_id` bigint(20) NOT NULL COMMENT '技师ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0=休息,1=工作,2=请假',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_technician_date` (`technician_id`, `work_date`),
  KEY `idx_work_date` (`work_date`),
  CONSTRAINT `fk_technician_schedules` FOREIGN KEY (`technician_id`) REFERENCES `technicians` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师排班表';

-- 插入示例数据（可选）
-- 注意：这里使用的shop_user_id=2假设存在ID为2的维修店用户，请根据实际情况调整
INSERT INTO `technicians` (`name`, `phone`, `experience`, `join_date`, `skills`, `description`, `shop_user_id`, `status`, `rating`, `total_services`, `monthly_income`) 
VALUES 
('张师傅', '13800138001', 3, '2023-01-15', '["发动机维修", "变速箱维修", "电路诊断"]', '资深技师，专业维修经验丰富，擅长处理各类发动机故障', 2, 1, 4.8, 145, 8500.00),
('李师傅', '13800138002', 4, '2022-06-20', '["刹车系统", "空调系统", "电子系统", "悬挂系统"]', '精通各类汽车电子系统维修，有丰富的进口车维修经验', 2, 1, 4.6, 120, 7800.00),
('王师傅', '13800138003', 2, '2024-03-10', '["轮胎服务", "美容养护", "基础保养"]', '年轻有为，服务态度优秀，专业细致', 2, 0, 4.3, 65, 4200.00)
ON DUPLICATE KEY UPDATE
`name` = VALUES(`name`),
`phone` = VALUES(`phone`),
`experience` = VALUES(`experience`),
`join_date` = VALUES(`join_date`),
`skills` = VALUES(`skills`),
`description` = VALUES(`description`),
`rating` = VALUES(`rating`),
`total_services` = VALUES(`total_services`),
`monthly_income` = VALUES(`monthly_income`);

-- 插入一些示例排班数据（可选）
INSERT INTO `technician_schedules` (`technician_id`, `work_date`, `start_time`, `end_time`, `status`)
SELECT t.id, CURDATE(), '09:00:00', '18:00:00', 1
FROM `technicians` t
WHERE t.shop_user_id = 2
ON DUPLICATE KEY UPDATE
`start_time` = VALUES(`start_time`),
`end_time` = VALUES(`end_time`),
`status` = VALUES(`status`);

-- 验证表创建是否成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM 
    information_schema.TABLES 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('technicians', 'technician_schedules');

-- 显示技师数据
SELECT 
    id,
    name,
    phone,
    CASE experience
        WHEN 1 THEN '1年以下'
        WHEN 2 THEN '1-3年'
        WHEN 3 THEN '3-5年'
        WHEN 4 THEN '5-10年'
        WHEN 5 THEN '10年以上'
        ELSE '未知'
    END as experience_text,
    join_date,
    JSON_EXTRACT(skills, '$') as skills,
    CASE status
        WHEN 0 THEN '离线'
        WHEN 1 THEN '在线'
        ELSE '未知'
    END as status_text,
    rating,
    total_services,
    monthly_income,
    create_time
FROM technicians 
ORDER BY create_time DESC;

-- 执行完成提示
SELECT '技师管理系统数据库初始化完成！' as message;
