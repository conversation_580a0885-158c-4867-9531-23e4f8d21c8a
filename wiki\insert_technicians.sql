USE car_service;

-- 插入技师测试数据
INSERT INTO technicians (name, phone, experience, join_date, skills, description, shop_user_id, status, rating, total_services, monthly_income, create_time, update_time) VALUES 
('张师傅', '13800138001', 3, '2023-01-15', '["发动机维修", "变速箱维修"]', '资深技师，专业维修经验丰富', 4, 1, 4.8, 145, 8500.00, NOW(), NOW()),
('李师傅', '13800138002', 4, '2022-06-20', '["刹车系统", "空调系统", "电子系统"]', '精通各类汽车电子系统维修', 4, 1, 4.6, 120, 7800.00, NOW(), NOW()),
('王师傅', '13800138003', 2, '2024-03-10', '["轮胎服务", "美容养护"]', '年轻有为，服务态度优秀', 4, 0, 4.3, 65, 4200.00, NOW(), NOW()),
('赵师傅', '13800138004', 3, '2023-06-01', '["发动机维修", "刹车系统"]', '经验丰富的维修专家', 6, 1, 4.7, 98, 6800.00, NOW(), NOW()),
('刘师傅', '13800138005', 2, '2024-01-20', '["电子系统", "空调系统"]', '年轻技师，技术过硬', 6, 1, 4.4, 56, 4500.00, NOW(), NOW());

-- 查看插入结果
SELECT * FROM technicians;
