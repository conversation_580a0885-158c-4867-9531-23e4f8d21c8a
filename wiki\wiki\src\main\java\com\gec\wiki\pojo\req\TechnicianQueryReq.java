package com.gec.wiki.pojo.req;

public class TechnicianQueryReq extends PageReq {
    
    private String keyword; // 搜索关键词（姓名或技能）
    
    private Long shopUserId; // 维修店用户ID
    
    private Integer status; // 状态筛选
    
    private Integer experience; // 经验筛选
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public Long getShopUserId() {
        return shopUserId;
    }
    
    public void setShopUserId(Long shopUserId) {
        this.shopUserId = shopUserId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getExperience() {
        return experience;
    }
    
    public void setExperience(Integer experience) {
        this.experience = experience;
    }
    
    @Override
    public String toString() {
        return "TechnicianQueryReq{" +
                "keyword='" + keyword + '\'' +
                ", shopUserId=" + shopUserId +
                ", status=" + status +
                ", experience=" + experience +
                ", page=" + getPage() +
                ", size=" + getSize() +
                '}';
    }
}
