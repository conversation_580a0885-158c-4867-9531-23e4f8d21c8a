-- 技师管理表
CREATE TABLE `technicians` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '技师ID',
  `name` varchar(50) NOT NULL COMMENT '技师姓名',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `experience` tinyint(4) NOT NULL COMMENT '工作经验：1=1年以下,2=1-3年,3=3-5年,4=5-10年,5=10年以上',
  `join_date` date DEFAULT NULL COMMENT '入职日期',
  `skills` json DEFAULT NULL COMMENT '专业技能(JSON格式)',
  `description` text COMMENT '技师简介',
  `shop_user_id` bigint(20) NOT NULL COMMENT '所属维修店用户ID',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0=离线,1=在线',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '评分(0-5分)',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `total_services` int(11) DEFAULT '0' COMMENT '总服务次数',
  `monthly_income` decimal(10,2) DEFAULT '0.00' COMMENT '本月收入',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_user_id` (`shop_user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_technicians_shop_user` FOREIGN KEY (`shop_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师信息表';

-- 技师技能关联表（如果需要更复杂的技能管理）
CREATE TABLE `technician_skills` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `technician_id` bigint(20) NOT NULL COMMENT '技师ID',
  `skill_name` varchar(50) NOT NULL COMMENT '技能名称',
  `skill_level` tinyint(4) DEFAULT '1' COMMENT '技能等级：1=初级,2=中级,3=高级',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_technician_id` (`technician_id`),
  CONSTRAINT `fk_technician_skills` FOREIGN KEY (`technician_id`) REFERENCES `technicians` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师技能关联表';

-- 技师排班表
CREATE TABLE `technician_schedules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `technician_id` bigint(20) NOT NULL COMMENT '技师ID',
  `work_date` date NOT NULL COMMENT '工作日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0=休息,1=工作,2=请假',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_technician_date` (`technician_id`, `work_date`),
  CONSTRAINT `fk_technician_schedules` FOREIGN KEY (`technician_id`) REFERENCES `technicians` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技师排班表';

-- 插入一些示例数据
INSERT INTO `technicians` (`name`, `phone`, `experience`, `join_date`, `skills`, `description`, `shop_user_id`, `status`, `rating`, `total_services`, `monthly_income`) 
VALUES 
('张师傅', '13800138001', 3, '2023-01-15', '["发动机维修", "变速箱维修"]', '资深技师，专业维修经验丰富', 2, 1, 4.8, 145, 8500.00),
('李师傅', '13800138002', 4, '2022-06-20', '["刹车系统", "空调系统", "电子系统"]', '精通各类汽车电子系统维修', 2, 1, 4.6, 120, 7800.00),
('王师傅', '13800138003', 2, '2024-03-10', '["轮胎服务", "美容养护"]', '年轻有为，服务态度优秀', 2, 0, 4.3, 65, 4200.00);
