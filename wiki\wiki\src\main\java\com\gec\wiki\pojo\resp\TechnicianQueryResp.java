package com.gec.wiki.pojo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class TechnicianQueryResp {
    
    private Long id;
    
    private String name;
    
    private String phone;
    
    private Integer experience;
    
    private String experienceText;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate joinDate;
    
    private List<String> skills;
    
    private String description;
    
    private Long shopUserId;
    
    private Integer status;
    
    private String statusText;
    
    private BigDecimal rating;
    
    private String avatar;
    
    private Integer totalServices;
    
    private BigDecimal monthlyIncome;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    // 业绩相关字段
    private Integer monthlyServices; // 本月服务次数
    private BigDecimal satisfaction; // 客户满意度
    private BigDecimal avgRating; // 平均评分
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Integer getExperience() {
        return experience;
    }
    
    public void setExperience(Integer experience) {
        this.experience = experience;
        // 自动设置experienceText
        setExperienceText(getExperienceDisplayText(experience));
    }
    
    public String getExperienceText() {
        return experienceText;
    }
    
    public void setExperienceText(String experienceText) {
        this.experienceText = experienceText;
    }
    
    public LocalDate getJoinDate() {
        return joinDate;
    }
    
    public void setJoinDate(LocalDate joinDate) {
        this.joinDate = joinDate;
    }
    
    public List<String> getSkills() {
        return skills;
    }
    
    public void setSkills(List<String> skills) {
        this.skills = skills;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Long getShopUserId() {
        return shopUserId;
    }
    
    public void setShopUserId(Long shopUserId) {
        this.shopUserId = shopUserId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
        // 自动设置statusText
        setStatusText(status != null && status == 1 ? "在线" : "离线");
    }
    
    public String getStatusText() {
        return statusText;
    }
    
    public void setStatusText(String statusText) {
        this.statusText = statusText;
    }
    
    public BigDecimal getRating() {
        return rating;
    }
    
    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    public Integer getTotalServices() {
        return totalServices;
    }
    
    public void setTotalServices(Integer totalServices) {
        this.totalServices = totalServices;
    }
    
    public BigDecimal getMonthlyIncome() {
        return monthlyIncome;
    }
    
    public void setMonthlyIncome(BigDecimal monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Integer getMonthlyServices() {
        return monthlyServices;
    }
    
    public void setMonthlyServices(Integer monthlyServices) {
        this.monthlyServices = monthlyServices;
    }
    
    public BigDecimal getSatisfaction() {
        return satisfaction;
    }
    
    public void setSatisfaction(BigDecimal satisfaction) {
        this.satisfaction = satisfaction;
    }
    
    public BigDecimal getAvgRating() {
        return avgRating;
    }
    
    public void setAvgRating(BigDecimal avgRating) {
        this.avgRating = avgRating;
    }
    
    /**
     * 获取工作经验文本
     */
    private String getExperienceDisplayText(Integer experience) {
        if (experience == null) return "未知";
        switch (experience) {
            case 1: return "1年以下";
            case 2: return "1-3年";
            case 3: return "3-5年";
            case 4: return "5-10年";
            case 5: return "10年以上";
            default: return "未知";
        }
    }
}
