package com.gec.wiki.service.impl;

import com.gec.wiki.exception.BusinessException;
import com.gec.wiki.exception.BusinessExceptionCode;
import com.gec.wiki.mapper.TechnicianMapper;
import com.gec.wiki.pojo.Technician;
import com.gec.wiki.pojo.req.TechnicianQueryReq;
import com.gec.wiki.pojo.req.TechnicianSaveReq;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.TechnicianQueryResp;
import com.gec.wiki.service.TechnicianService;
import com.gec.wiki.utils.CopyUtil;
import com.gec.wiki.utils.SnowFlake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Service
public class TechnicianServiceImpl implements TechnicianService {
    
    private static final Logger LOG = LoggerFactory.getLogger(TechnicianServiceImpl.class);
    
    @Resource
    private TechnicianMapper technicianMapper;
    
    @Resource
    private SnowFlake snowFlake;
    
    @Override
    public PageResp<TechnicianQueryResp> list(TechnicianQueryReq req) {
        LOG.info("查询技师列表参数: {}", req);
        
        // 设置分页参数
        if (req.getPage() == null || req.getPage() < 1) {
            req.setPage(1);
        }
        if (req.getSize() == null || req.getSize() < 1) {
            req.setSize(10);
        }
        
        // 计算偏移量
        int offset = (req.getPage() - 1) * req.getSize();
        req.setPage(offset);
        
        PageResp<TechnicianQueryResp> pageResp = new PageResp<>();
        
        // 查询总数
        int total = technicianMapper.selectCount(req);
        pageResp.setTotal(total);
        
        // 查询列表数据
        List<TechnicianQueryResp> list = technicianMapper.selectList(req);
        pageResp.setList(list);
        
        LOG.info("查询技师列表结果: 总数={}, 当前页数据量={}", total, list.size());
        return pageResp;
    }
    
    @Override
    @Transactional
    public void save(TechnicianSaveReq req) {
        LOG.info("保存技师信息: {}", req);
        
        // 验证必填字段
        if (!StringUtils.hasText(req.getName())) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师姓名不能为空");
        }
        if (!StringUtils.hasText(req.getPhone())) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "联系电话不能为空");
        }
        if (req.getExperience() == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "工作经验不能为空");
        }
        if (req.getShopUserId() == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        // 检查电话号码是否重复
        boolean phoneExists = checkPhoneExists(req.getPhone(), req.getId(), req.getShopUserId());
        if (phoneExists) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "该电话号码已存在");
        }
        
        Technician technician = CopyUtil.copy(req, Technician.class);
        
        if (req.getId() != null) {
            // 更新
            Technician existTechnician = technicianMapper.selectById(req.getId());
            if (existTechnician == null) {
                throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师不存在");
            }
            if (!existTechnician.getShopUserId().equals(req.getShopUserId())) {
                throw new BusinessException(BusinessExceptionCode.PERMISSION_ERROR, "无权限修改此技师信息");
            }
            
            technician.setUpdateTime(LocalDateTime.now());
            int updateCount = technicianMapper.updateById(technician);
            if (updateCount == 0) {
                throw new BusinessException(BusinessExceptionCode.SYSTEM_ERROR, "更新技师信息失败");
            }
            LOG.info("更新技师信息成功，ID: {}", req.getId());
            
        } else {
            // 新增
            technician.setId(snowFlake.nextId());
            technician.setStatus(1); // 默认在线
            technician.setRating(new BigDecimal("5.00")); // 默认5分
            technician.setTotalServices(0);
            technician.setMonthlyIncome(new BigDecimal("0.00"));
            technician.setCreateTime(LocalDateTime.now());
            technician.setUpdateTime(LocalDateTime.now());
            
            int insertCount = technicianMapper.insert(technician);
            if (insertCount == 0) {
                throw new BusinessException(BusinessExceptionCode.SYSTEM_ERROR, "新增技师信息失败");
            }
            LOG.info("新增技师信息成功，ID: {}", technician.getId());
        }
    }
    
    @Override
    public TechnicianQueryResp getById(Long id, Long shopUserId) {
        LOG.info("根据ID查询技师详情: id={}, shopUserId={}", id, shopUserId);
        
        if (id == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师ID不能为空");
        }
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        Technician technician = technicianMapper.selectById(id);
        if (technician == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师不存在");
        }
        
        // 验证权限
        if (!technician.getShopUserId().equals(shopUserId)) {
            throw new BusinessException(BusinessExceptionCode.PERMISSION_ERROR, "无权限查看此技师信息");
        }
        
        TechnicianQueryResp resp = CopyUtil.copy(technician, TechnicianQueryResp.class);
        
        // 获取业绩信息
        Map<String, Object> performance = getPerformance(id, shopUserId);
        if (performance != null) {
            if (performance.containsKey("monthlyServices")) {
                resp.setMonthlyServices((Integer) performance.get("monthlyServices"));
            }
            if (performance.containsKey("satisfaction")) {
                resp.setSatisfaction(new BigDecimal(performance.get("satisfaction").toString()));
            }
        }
        
        LOG.info("查询技师详情成功: {}", resp);
        return resp;
    }
    
    @Override
    @Transactional
    public void deleteById(Long id, Long shopUserId) {
        LOG.info("删除技师: id={}, shopUserId={}", id, shopUserId);
        
        if (id == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师ID不能为空");
        }
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        Technician technician = technicianMapper.selectById(id);
        if (technician == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师不存在");
        }
        
        // 验证权限
        if (!technician.getShopUserId().equals(shopUserId)) {
            throw new BusinessException(BusinessExceptionCode.PERMISSION_ERROR, "无权限删除此技师");
        }
        
        // TODO: 检查技师是否有未完成的订单，如果有则不能删除
        
        int deleteCount = technicianMapper.deleteById(id);
        if (deleteCount == 0) {
            throw new BusinessException(BusinessExceptionCode.SYSTEM_ERROR, "删除技师失败");
        }
        
        LOG.info("删除技师成功: {}", id);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> ids, Long shopUserId) {
        LOG.info("批量删除技师: ids={}, shopUserId={}", ids, shopUserId);
        
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师ID列表不能为空");
        }
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        for (Long id : ids) {
            try {
                deleteById(id, shopUserId);
            } catch (Exception e) {
                LOG.error("删除技师失败: id={}, error={}", id, e.getMessage());
                throw new BusinessException(BusinessExceptionCode.SYSTEM_ERROR, "批量删除技师失败");
            }
        }
        
        LOG.info("批量删除技师成功");
    }
    
    @Override
    @Transactional
    public void updateStatus(Long id, Integer status, Long shopUserId) {
        LOG.info("更新技师状态: id={}, status={}, shopUserId={}", id, status, shopUserId);
        
        if (id == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师ID不能为空");
        }
        if (status == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "状态不能为空");
        }
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        Technician technician = technicianMapper.selectById(id);
        if (technician == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "技师不存在");
        }
        
        // 验证权限
        if (!technician.getShopUserId().equals(shopUserId)) {
            throw new BusinessException(BusinessExceptionCode.PERMISSION_ERROR, "无权限修改此技师状态");
        }
        
        int updateCount = technicianMapper.updateStatus(id, status);
        if (updateCount == 0) {
            throw new BusinessException(BusinessExceptionCode.SYSTEM_ERROR, "更新技师状态失败");
        }
        
        LOG.info("更新技师状态成功");
    }
    
    @Override
    public Map<String, Object> getStats(Long shopUserId) {
        LOG.info("获取技师统计信息: shopUserId={}", shopUserId);
        
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        Map<String, Object> stats = technicianMapper.selectStats(shopUserId);
        LOG.info("技师统计信息: {}", stats);
        return stats;
    }
    
    @Override
    public List<TechnicianQueryResp> getOnlineTechnicians(Long shopUserId) {
        LOG.info("获取在线技师列表: shopUserId={}", shopUserId);
        
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        List<TechnicianQueryResp> list = technicianMapper.selectOnlineTechnicians(shopUserId);
        LOG.info("在线技师数量: {}", list.size());
        return list;
    }
    
    @Override
    public List<TechnicianQueryResp> searchBySkills(List<String> skills, Long shopUserId) {
        LOG.info("根据技能搜索技师: skills={}, shopUserId={}", skills, shopUserId);
        
        if (shopUserId == null) {
            throw new BusinessException(BusinessExceptionCode.PARAM_ERROR, "维修店用户ID不能为空");
        }
        
        List<TechnicianQueryResp> list = technicianMapper.selectBySkills(skills, shopUserId);
        LOG.info("匹配技师数量: {}", list.size());
        return list;
    }
    
    @Override
    public Map<String, Object> getPerformance(Long technicianId, Long shopUserId) {
        LOG.info("获取技师业绩信息: technicianId={}, shopUserId={}", technicianId, shopUserId);
        
        if (technicianId == null || shopUserId == null) {
            return null;
        }
        
        Map<String, Object> performance = technicianMapper.selectPerformance(technicianId, shopUserId);
        LOG.info("技师业绩信息: {}", performance);
        return performance;
    }
    
    @Override
    public boolean checkPhoneExists(String phone, Long excludeId, Long shopUserId) {
        if (!StringUtils.hasText(phone) || shopUserId == null) {
            return false;
        }
        
        int count = technicianMapper.countByPhone(phone, excludeId, shopUserId);
        return count > 0;
    }
}
