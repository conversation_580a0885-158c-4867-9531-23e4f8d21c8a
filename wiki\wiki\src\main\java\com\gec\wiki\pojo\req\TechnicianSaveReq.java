package com.gec.wiki.pojo.req;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;

public class TechnicianSaveReq {
    
    private Long id;
    
    @NotBlank(message = "技师姓名不能为空")
    private String name;
    
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    private String phone;
    
    @NotNull(message = "请选择工作经验")
    private Integer experience;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate joinDate;
    
    private List<String> skills;
    
    private String description;
    
    @NotNull(message = "维修店用户ID不能为空")
    private Long shopUserId;
    
    private Integer status = 1; // 默认在线
    
    private String avatar;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Integer getExperience() {
        return experience;
    }
    
    public void setExperience(Integer experience) {
        this.experience = experience;
    }
    
    public LocalDate getJoinDate() {
        return joinDate;
    }
    
    public void setJoinDate(LocalDate joinDate) {
        this.joinDate = joinDate;
    }
    
    public List<String> getSkills() {
        return skills;
    }
    
    public void setSkills(List<String> skills) {
        this.skills = skills;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Long getShopUserId() {
        return shopUserId;
    }
    
    public void setShopUserId(Long shopUserId) {
        this.shopUserId = shopUserId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    @Override
    public String toString() {
        return "TechnicianSaveReq{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", experience=" + experience +
                ", joinDate=" + joinDate +
                ", skills=" + skills +
                ", description='" + description + '\'' +
                ", shopUserId=" + shopUserId +
                ", status=" + status +
                ", avatar='" + avatar + '\'' +
                '}';
    }
}
