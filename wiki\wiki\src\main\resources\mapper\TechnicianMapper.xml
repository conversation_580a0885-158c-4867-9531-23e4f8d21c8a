<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gec.wiki.mapper.TechnicianMapper">

    <!-- 技师基础字段映射 -->
    <sql id="Base_Column_List">
        id, name, phone, experience, join_date, skills, description, 
        shop_user_id, status, rating, avatar, total_services, monthly_income,
        create_time, update_time
    </sql>

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.Technician">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="experience" property="experience" jdbcType="TINYINT"/>
        <result column="join_date" property="joinDate" jdbcType="DATE"/>
        <result column="skills" property="skills" jdbcType="VARCHAR" 
                typeHandler="com.gec.wiki.utils.JsonTypeHandler"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="shop_user_id" property="shopUserId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="rating" property="rating" jdbcType="DECIMAL"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="total_services" property="totalServices" jdbcType="INTEGER"/>
        <result column="monthly_income" property="monthlyIncome" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 查询响应映射 -->
    <resultMap id="QueryResponseMap" type="com.gec.wiki.pojo.resp.TechnicianQueryResp">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="experience" property="experience" jdbcType="TINYINT"/>
        <result column="join_date" property="joinDate" jdbcType="DATE"/>
        <result column="skills" property="skills" jdbcType="VARCHAR" 
                typeHandler="com.gec.wiki.utils.JsonTypeHandler"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="shop_user_id" property="shopUserId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="rating" property="rating" jdbcType="DECIMAL"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="total_services" property="totalServices" jdbcType="INTEGER"/>
        <result column="monthly_income" property="monthlyIncome" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM technicians
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 分页查询技师列表 -->
    <select id="selectList" parameterType="com.gec.wiki.pojo.req.TechnicianQueryReq" resultMap="QueryResponseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM technicians
        <where>
            <if test="shopUserId != null">
                AND shop_user_id = #{shopUserId,jdbcType=BIGINT}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    name LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
                    OR JSON_SEARCH(skills, 'one', #{keyword,jdbcType=VARCHAR}) IS NOT NULL
                )
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=TINYINT}
            </if>
            <if test="experience != null">
                AND experience = #{experience,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="page != null and size != null">
            LIMIT #{page,jdbcType=INTEGER}, #{size,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 查询技师总数 -->
    <select id="selectCount" parameterType="com.gec.wiki.pojo.req.TechnicianQueryReq" resultType="int">
        SELECT COUNT(*)
        FROM technicians
        <where>
            <if test="shopUserId != null">
                AND shop_user_id = #{shopUserId,jdbcType=BIGINT}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    name LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
                    OR JSON_SEARCH(skills, 'one', #{keyword,jdbcType=VARCHAR}) IS NOT NULL
                )
            </if>
            <if test="status != null">
                AND status = #{status,jdbcType=TINYINT}
            </if>
            <if test="experience != null">
                AND experience = #{experience,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <!-- 新增技师 -->
    <insert id="insert" parameterType="com.gec.wiki.pojo.Technician">
        INSERT INTO technicians (
            id, name, phone, experience, join_date, skills, description,
            shop_user_id, status, rating, avatar, total_services, monthly_income,
            create_time, update_time
        ) VALUES (
            #{id,jdbcType=BIGINT},
            #{name,jdbcType=VARCHAR},
            #{phone,jdbcType=VARCHAR},
            #{experience,jdbcType=TINYINT},
            #{joinDate,jdbcType=DATE},
            #{skills,jdbcType=VARCHAR,typeHandler=com.gec.wiki.utils.JsonTypeHandler},
            #{description,jdbcType=LONGVARCHAR},
            #{shopUserId,jdbcType=BIGINT},
            #{status,jdbcType=TINYINT},
            #{rating,jdbcType=DECIMAL},
            #{avatar,jdbcType=VARCHAR},
            #{totalServices,jdbcType=INTEGER},
            #{monthlyIncome,jdbcType=DECIMAL},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 更新技师信息 -->
    <update id="updateById" parameterType="com.gec.wiki.pojo.Technician">
        UPDATE technicians
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="experience != null">
                experience = #{experience,jdbcType=TINYINT},
            </if>
            <if test="joinDate != null">
                join_date = #{joinDate,jdbcType=DATE},
            </if>
            <if test="skills != null">
                skills = #{skills,jdbcType=VARCHAR,typeHandler=com.gec.wiki.utils.JsonTypeHandler},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="rating != null">
                rating = #{rating,jdbcType=DECIMAL},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="totalServices != null">
                total_services = #{totalServices,jdbcType=INTEGER},
            </if>
            <if test="monthlyIncome != null">
                monthly_income = #{monthlyIncome,jdbcType=DECIMAL},
            </if>
            update_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除技师 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM technicians
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据维修店用户ID删除所有技师 -->
    <delete id="deleteByShopUserId" parameterType="java.lang.Long">
        DELETE FROM technicians
        WHERE shop_user_id = #{shopUserId,jdbcType=BIGINT}
    </delete>

    <!-- 检查电话号码是否存在 -->
    <select id="countByPhone" resultType="int">
        SELECT COUNT(*)
        FROM technicians
        WHERE phone = #{phone,jdbcType=VARCHAR}
        AND shop_user_id = #{shopUserId,jdbcType=BIGINT}
        <if test="excludeId != null">
            AND id != #{excludeId,jdbcType=BIGINT}
        </if>
    </select>

    <!-- 更新技师状态 -->
    <update id="updateStatus">
        UPDATE technicians
        SET status = #{status,jdbcType=TINYINT},
            update_time = CURRENT_TIMESTAMP
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 获取技师统计信息 -->
    <select id="selectStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as online,
            SUM(total_services) as todayServices,
            AVG(rating) as avgRating
        FROM technicians
        WHERE shop_user_id = #{shopUserId,jdbcType=BIGINT}
    </select>

    <!-- 获取技师业绩信息 -->
    <select id="selectPerformance" resultType="java.util.Map">
        SELECT 
            t.total_services as monthlyServices,
            95 as satisfaction,
            t.monthly_income as monthlyIncome,
            t.rating as avgRating
        FROM technicians t
        WHERE t.id = #{technicianId,jdbcType=BIGINT}
        AND t.shop_user_id = #{shopUserId,jdbcType=BIGINT}
    </select>

    <!-- 批量更新技师评分 -->
    <update id="batchUpdateRating">
        <foreach collection="updates" item="item" separator=";">
            UPDATE technicians 
            SET rating = #{item.rating,jdbcType=DECIMAL},
                total_services = #{item.totalServices,jdbcType=INTEGER},
                update_time = CURRENT_TIMESTAMP
            WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!-- 根据技能搜索技师 -->
    <select id="selectBySkills" resultMap="QueryResponseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM technicians
        WHERE shop_user_id = #{shopUserId,jdbcType=BIGINT}
        <if test="skills != null and skills.size() > 0">
            AND (
            <foreach collection="skills" item="skill" separator=" OR ">
                JSON_SEARCH(skills, 'one', #{skill,jdbcType=VARCHAR}) IS NOT NULL
            </foreach>
            )
        </if>
        ORDER BY rating DESC, total_services DESC
    </select>

    <!-- 获取在线技师列表 -->
    <select id="selectOnlineTechnicians" resultMap="QueryResponseMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM technicians
        WHERE shop_user_id = #{shopUserId,jdbcType=BIGINT}
        AND status = 1
        ORDER BY rating DESC, total_services DESC
    </select>

</mapper>
